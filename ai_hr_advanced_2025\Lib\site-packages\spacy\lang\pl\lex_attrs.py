from ...attrs import LIKE_NUM

_num_words = [
    "zero",
    "jeden",
    "dwa",
    "trzy",
    "cztery",
    "pięć",
    "sześć",
    "siedem",
    "osiem",
    "dzie<PERSON><PERSON><PERSON>",
    "dzie<PERSON><PERSON><PERSON>",
    "jedenaś<PERSON>",
    "dwana<PERSON>cie",
    "trzynaście",
    "czternaście",
    "pietnaście",
    "szesnaście",
    "siedemnaście",
    "osiemnaście",
    "dziewiętnaście",
    "dwadzieścia",
    "trzydzieści",
    "czterdzieści",
    "pięćdziesiąt",
    "szcześćdziesiąt",
    "siedemdziesiąt",
    "osiemdziesiąt",
    "dziewięćdziesiąt",
    "sto",
    "dwieście",
    "trzysta",
    "czterysta",
    "pięćset",
    "sześćset",
    "siedemset",
    "osiemset",
    "dziewi<PERSON><PERSON>set",
    "tysiąc",
    "milion",
    "miliard",
    "bilion",
    "biliard",
    "trylion",
    "tryliard",
    "kwadrylion",
]


def like_num(text):
    text = text.replace(",", "").replace(".", "")
    if text.isdigit():
        return True
    if text.count("/") == 1:
        num, denom = text.split("/")
        if num.isdigit() and denom.isdigit():
            return True
    if text.lower() in _num_words:
        return True
    return False


LEX_ATTRS = {LIKE_NUM: like_num}
