import{r as s,bi as U,z as k,ct as W,C as A,j as l,bs as L,bG as P,bt as B,bb as D,bu as j}from"./index.C1z8KpLA.js";import{u as _}from"./uniqueId.j-1rlNNH.js";import{u as w,a as G,b as K}from"./useOnInputChange.z04u96A8.js";import{I as O}from"./InputInstructions.D-Y8geDN.js";import{a as $}from"./useBasicWidgetState.zXY9CjFS.js";import{T as q}from"./textarea.BR8rlyih.js";import"./inputUtils.CQWz5UKz.js";import"./FormClearHelper.B67tgll0.js";import"./base-input.BoAa1U94.js";const x=(a,t)=>a.getStringValue(t)??t.default??null,N=a=>a.default??null,J=a=>a.value??null,Q=(a,t,o,u)=>{t.setStringValue(a,o.value,{fromUi:o.fromUi},u)},X=({disabled:a,element:t,widgetMgr:o,fragmentId:u})=>{var f;const h=s.useRef(_("text_area_")).current,[T,S]=U(),[r,n]=s.useState(!1),[C,b]=s.useState(!1),[i,d]=s.useState(()=>x(o,t)??null),I=s.useCallback(()=>{d(t.default??null),n(!0)},[t]),[y,c]=$({getStateFromWidgetMgr:x,getDefaultStateFromProto:N,getCurrStateFromProto:J,updateWidgetMgrState:Q,element:t,widgetMgr:o,fragmentId:u,onFormCleared:I});w(y,i,d,r);const e=k(),m=s.useCallback(()=>{n(!1),c({value:i,fromUi:!0})},[i,c]),V=s.useCallback(()=>{r&&m(),b(!1)},[r,m]),F=s.useCallback(()=>{b(!0)},[]),v=G({formId:t.formId,maxChars:t.maxChars,setDirty:n,setUiValue:d,setValueWithSource:c}),z=K(t.formId,m,r,o,u,!0),{height:g,placeholder:E,formId:p}=t,R=W({formId:p})?o.allowFormEnterToSubmit(p):r,H=C&&T>e.breakpoints.hideWidgetDetails;return A("div",{className:"stTextArea","data-testid":"stTextArea",ref:S,children:[l(j,{label:t.label,disabled:a,labelVisibility:L((f=t.labelVisibility)==null?void 0:f.value),htmlFor:h,children:t.help&&l(P,{children:l(B,{content:t.help,placement:D.TOP_RIGHT})})}),l(q,{value:i??"",placeholder:E,onBlur:V,onFocus:F,onChange:v,onKeyDown:z,"aria-label":t.label,disabled:a,id:h,overrides:{Input:{style:{lineHeight:e.lineHeights.inputWidget,height:g?`${g}px`:"",minHeight:e.sizes.largestElementHeight,resize:"vertical","::placeholder":{opacity:"0.7"},paddingRight:e.spacing.md,paddingLeft:e.spacing.md,paddingBottom:e.spacing.md,paddingTop:e.spacing.md}},Root:{props:{"data-testid":"stTextAreaRootElement"},style:{borderLeftWidth:e.sizes.borderWidth,borderRightWidth:e.sizes.borderWidth,borderTopWidth:e.sizes.borderWidth,borderBottomWidth:e.sizes.borderWidth}}}}),H&&l(O,{dirty:r,value:i??"",maxLength:t.maxChars,type:"multiline",inForm:W({formId:p}),allowEnterToSubmit:R})]})},it=s.memo(X);export{it as default};
