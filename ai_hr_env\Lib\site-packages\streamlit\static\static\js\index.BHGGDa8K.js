import{bw as Z,bx as J,r as f,by as Q,z as Y,bz as s,j as v,C as ee,bs as te,bA as re,bt as ne,bb as oe,bB as ie,bu as ae,bq as ue,b6 as C,b5 as W,b8 as le}from"./index.C1z8KpLA.js";import{a as ce}from"./useBasicWidgetState.zXY9CjFS.js";import"./FormClearHelper.B67tgll0.js";var se={secondary:"secondary"},G={default:"default"},fe={default:"default"},k=Object.freeze({radio:"radio",checkbox:"checkbox"}),z=Z("div",function(e){var t=e.$shape,r=e.$length,n=e.$theme,o=r===1?void 0:t!==G.default?"-".concat(n.sizing.scale100):"-0.5px";return{display:"flex",marginLeft:o,marginRight:o}});z.displayName="StyledRoot";z.displayName="StyledRoot";function j(e){"@babel/helpers - typeof";return j=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},j(e)}function M(){return M=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},M.apply(this,arguments)}function x(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),r.push.apply(r,n)}return r}function pe(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?x(Object(r),!0).forEach(function(n){N(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):x(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function de(e,t){return me(e)||he(e,t)||be(e,t)||ye()}function ye(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function be(e,t){if(e){if(typeof e=="string")return X(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return X(e,t)}}function X(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function he(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n=[],o=!0,i=!1,a,p;try{for(r=r.call(e);!(o=(a=r.next()).done)&&(n.push(a.value),!(t&&n.length===t));o=!0);}catch(c){i=!0,p=c}finally{try{!o&&r.return!=null&&r.return()}finally{if(i)throw p}}return n}}function me(e){if(Array.isArray(e))return e}function ge(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Se(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function ve(e,t,r){return t&&Se(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function Oe(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&$(e,t)}function $(e,t){return $=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,o){return n.__proto__=o,n},$(e,t)}function Ee(e){var t=we();return function(){var n=A(e),o;if(t){var i=A(this).constructor;o=Reflect.construct(n,arguments,i)}else o=n.apply(this,arguments);return _e(this,o)}}function _e(e,t){if(t&&(j(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return K(e)}function K(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function we(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch{return!1}}function A(e){return A=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},A(e)}function N(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Le(e,t){return!Array.isArray(e)&&typeof e!="number"?!1:Array.isArray(e)?e.includes(t):e===t}var F=function(e){Oe(r,e);var t=Ee(r);function r(){var n;ge(this,r);for(var o=arguments.length,i=new Array(o),a=0;a<o;a++)i[a]=arguments[a];return n=t.call.apply(t,[this].concat(i)),N(K(n),"childRefs",{}),n}return ve(r,[{key:"render",value:function(){var o=this,i=this.props,a=i.overrides,p=a===void 0?{}:a,c=i.mode,P=c===void 0?k.checkbox:c,d=i.children,y=i.selected,h=i.disabled,l=i.onClick,_=i.kind,m=i.shape,w=i.size,B=J(p.Root,z),O=de(B,2),E=O[0],L=O[1],H=this.props["aria-label"]||this.props.ariaLabel,g=P===k.radio,V=f.Children.count(d);return f.createElement(Q.Consumer,null,function(q){return f.createElement(E,M({"aria-label":H||q.buttongroup.ariaLabel,"data-baseweb":"button-group",role:g?"radiogroup":"group",$shape:m,$length:d.length},L),f.Children.map(d,function(b,R){if(!f.isValidElement(b))return null;var I=b.props.isSelected?b.props.isSelected:Le(y,R);return g&&(o.childRefs[R]=f.createRef()),f.cloneElement(b,{disabled:h||b.props.disabled,isSelected:I,ref:g?o.childRefs[R]:void 0,tabIndex:!g||I||g&&(!y||y===-1)&&R===0?0:-1,onKeyDown:function(u){if(g){var S=Number(y)?Number(y):0;if(u.key==="ArrowUp"||u.key==="ArrowLeft"){u.preventDefault&&u.preventDefault();var T=S-1<0?V-1:S-1;l&&l(u,T),o.childRefs[T].current&&o.childRefs[T].current.focus()}if(u.key==="ArrowDown"||u.key==="ArrowRight"){u.preventDefault&&u.preventDefault();var D=S+1>V-1?0:S+1;l&&l(u,D),o.childRefs[D].current&&o.childRefs[D].current.focus()}}},kind:_,onClick:function(u){h||(b.props.onClick&&b.props.onClick(u),l&&l(u,R))},shape:m,size:w,overrides:pe({BaseButton:{style:function(u){var S=u.$theme;return d.length===1?{}:m!==G.default?{marginLeft:S.sizing.scale100,marginRight:S.sizing.scale100}:{marginLeft:"0.5px",marginRight:"0.5px"}},props:{"aria-checked":I,role:g?"radio":"checkbox"}}},b.props.overrides)})}))})}}]),r}(f.Component);N(F,"defaultProps",{disabled:!1,onClick:function(){},shape:G.default,size:fe.default,kind:se.secondary});function Re(e,t){return t.includes(e)?t.filter(r=>r!==e):[...t,e]}function Ce(e,t,r){return e==s.ClickMode.MULTI_SELECT?Re(t,r??[]):r!=null&&r.includes(t)?[]:[t]}function Pe(e){return e.length===0?-1:e[0]}function ke(e,t,r,n){t.setIntArrayValue(e,r.value,{fromUi:r.fromUi},n)}function Ae(e,t,r){const n=r===s.Style.PILLS?C.PILLS:r===s.Style.BORDERLESS?C.BORDERLESS_ICON:C.SEGMENTED_CONTROL,o=r===s.Style.BORDERLESS?W.XSMALL:W.MEDIUM,i=n===C.PILLS||n===C.SEGMENTED_CONTROL,a=r===s.Style.BORDERLESS?"lg":"base";return{element:v(le,{icon:t,label:e,iconSize:a,useSmallerFont:i}),kind:n,size:o}}function Be(e,t,r,n){return r.indexOf(n)>-1?!0:t!==s.ClickMode.SINGLE_SELECT||e!==s.SelectionVisualization.ALL_UP_TO_SELECTED?!1:r.length>0&&n<r[0]}function Ie(e,t){return e&&(t=`${t}Active`),t}function Te(e,t){const r={flexWrap:"wrap",maxWidth:"fit-content"};switch(e){case s.Style.BORDERLESS:return{...r,columnGap:t.threeXS,rowGap:t.threeXS};case s.Style.PILLS:return{...r,columnGap:t.twoXS,rowGap:t.twoXS};case s.Style.SEGMENTED_CONTROL:return{...r,columnGap:t.none,rowGap:t.twoXS,"::after":{content:"''",flex:1e4}};default:return r}}function De(e,t,r,n,o,i){const a=Be(r,n,o,t);let p=e.content,c=e.contentIcon;return a&&(p=e.selectedContent?e.selectedContent:p,c=e.selectedContentIcon?e.selectedContentIcon:c),f.forwardRef(function(d,y){const{element:h,kind:l,size:_}=Ae(p??"",c??void 0,i),m=Ie(!!(a&&!e.selectedContent&&!e.selectedContentIcon),l);return v(ue,{...d,size:_,kind:m,children:h})})}function je(e,t){return e.getIntArrayValue(t)}function Me(e){return e.default??null}function $e(e){return e.value??null}function Ge(e){const{disabled:t,element:r,fragmentId:n,widgetMgr:o}=e,{clickMode:i,options:a,selectionVisualization:p,style:c,label:P,labelVisibility:d,help:y}=r,h=Y(),[l,_]=ce({getStateFromWidgetMgr:je,getDefaultStateFromProto:Me,getCurrStateFromProto:$e,updateWidgetMgrState:ke,element:r,widgetMgr:o,fragmentId:n}),m=(O,E)=>{const L=Ce(i,E,l);_({value:L,fromUi:!0})};let w;i===s.ClickMode.SINGLE_SELECT?w=k.radio:i===s.ClickMode.MULTI_SELECT&&(w=k.checkbox);const B=f.useMemo(()=>a.map((O,E)=>{const L=De(O,E,p,i,l,c);return v(L,{},`${O.content}-${E}`)}),[i,a,p,c,l]);return ee("div",{className:"stButtonGroup","data-testid":"stButtonGroup",children:[v(ae,{label:P,disabled:t,labelVisibility:te((d==null?void 0:d.value)??ie.LabelVisibilityOptions.COLLAPSED),children:y&&v(re,{children:v(ne,{content:y,placement:oe.TOP})})}),v(F,{disabled:t,mode:w,onClick:m,selected:i===s.ClickMode.MULTI_SELECT?l:Pe(l),overrides:{Root:{style:f.useCallback(()=>Te(c,h.spacing),[c,h.spacing])}},children:B})]})}const Ue=f.memo(Ge);export{Ue as default};
