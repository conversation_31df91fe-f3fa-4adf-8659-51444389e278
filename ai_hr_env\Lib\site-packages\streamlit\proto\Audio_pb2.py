# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: streamlit/proto/Audio.proto
# Protobuf Python Version: 5.26.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1bstreamlit/proto/Audio.proto\"\x86\x01\n\x05\x41udio\x12\x0b\n\x03url\x18\x05 \x01(\t\x12\x12\n\nstart_time\x18\x03 \x01(\x05\x12\x10\n\x08\x65nd_time\x18\x06 \x01(\x05\x12\x0c\n\x04loop\x18\x07 \x01(\x08\x12\x10\n\x08\x61utoplay\x18\x08 \x01(\x08\x12\n\n\x02id\x18\t \x01(\tJ\x04\x08\x01\x10\x02J\x04\x08\x02\x10\x03J\x04\x08\x04\x10\x05R\x04\x64\x61taR\x06\x66ormatB*\n\x1c\x63om.snowflake.apps.streamlitB\nAudioProtob\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'streamlit.proto.Audio_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\034com.snowflake.apps.streamlitB\nAudioProto'
  _globals['_AUDIO']._serialized_start=32
  _globals['_AUDIO']._serialized_end=166
# @@protoc_insertion_point(module_scope)
