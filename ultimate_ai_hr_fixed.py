"""
🚀 ULTIMATE AI HR 2025 - COMPATIBILITY FIXED
State-of-the-art AI HR system with resolved PyTorch-Streamlit compatibility
"""

import streamlit as st
import json
import time
import uuid
import requests
import asyncio
import threading
from datetime import datetime, timedelta
import numpy as np
import re
import io
import base64
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# Core libraries with version checking
try:
    import pandas as pd
    PANDAS_VERSION = pd.__version__
    PANDAS_AVAILABLE = True
except ImportError:
    PANDAS_AVAILABLE = False
    PANDAS_VERSION = "Not installed"

try:
    import plotly.express as px
    import plotly.graph_objects as go
    from plotly.subplots import make_subplots
    PLOTLY_VERSION = px.__version__ if hasattr(px, '__version__') else "Available"
    PLOTLY_AVAILABLE = True
except ImportError:
    PLOTLY_AVAILABLE = False
    PLOTLY_VERSION = "Not installed"

# Advanced AI libraries with graceful fallbacks
try:
    from textblob import TextBlob
    SENTIMENT_AVAILABLE = True
    SENTIMENT_VERSION = "TextBlob Available"
except ImportError:
    SENTIMENT_AVAILABLE = False
    SENTIMENT_VERSION = "Not installed"

try:
    import nltk
    NLTK_AVAILABLE = True
    NLTK_VERSION = nltk.__version__
except ImportError:
    NLTK_AVAILABLE = False
    NLTK_VERSION = "Not installed"

try:
    import speech_recognition as sr
    import pyttsx3
    VOICE_AVAILABLE = True
    VOICE_VERSION = f"SpeechRecognition {sr.__version__}"
except ImportError:
    VOICE_AVAILABLE = False
    VOICE_VERSION = "Not installed"

try:
    import sklearn
    SKLEARN_AVAILABLE = True
    SKLEARN_VERSION = sklearn.__version__
except ImportError:
    SKLEARN_AVAILABLE = False
    SKLEARN_VERSION = "Not installed"

# Advanced libraries with compatibility checks
try:
    import spacy
    SPACY_AVAILABLE = True
    SPACY_VERSION = spacy.__version__
except ImportError:
    SPACY_AVAILABLE = False
    SPACY_VERSION = "Not installed"

# Delayed PyTorch import to avoid Streamlit conflicts
TORCH_AVAILABLE = False
TORCH_VERSION = "Not installed"
TRANSFORMERS_AVAILABLE = False
TRANSFORMERS_VERSION = "Not installed"

def safe_import_torch():
    """Safely import PyTorch after Streamlit initialization"""
    global TORCH_AVAILABLE, TORCH_VERSION, TRANSFORMERS_AVAILABLE, TRANSFORMERS_VERSION
    try:
        import torch
        TORCH_AVAILABLE = True
        TORCH_VERSION = torch.__version__
        
        import transformers
        TRANSFORMERS_AVAILABLE = True
        TRANSFORMERS_VERSION = transformers.__version__
        
        return True
    except Exception as e:
        st.warning(f"PyTorch/Transformers import warning: {e}")
        return False

# Advanced AI capabilities detection
FAIRLEARN_AVAILABLE = False
XGBOOST_AVAILABLE = False
LIGHTGBM_AVAILABLE = False

try:
    import fairlearn
    FAIRLEARN_AVAILABLE = True
    FAIRLEARN_VERSION = fairlearn.__version__
except ImportError:
    FAIRLEARN_VERSION = "Not installed"

try:
    import xgboost
    XGBOOST_AVAILABLE = True
    XGBOOST_VERSION = xgboost.__version__
except ImportError:
    XGBOOST_VERSION = "Not installed"

try:
    import lightgbm
    LIGHTGBM_AVAILABLE = True
    LIGHTGBM_VERSION = lightgbm.__version__
except ImportError:
    LIGHTGBM_VERSION = "Not installed"

# Page configuration with advanced settings
st.set_page_config(
    page_title="🚀 Ultimate AI HR 2025 - Fixed",
    page_icon="🚀",
    layout="wide",
    initial_sidebar_state="expanded",
    menu_items={
        'Get Help': 'https://github.com/ai-hr-2025',
        'Report a bug': 'https://github.com/ai-hr-2025/issues',
        'About': "Ultimate AI HR 2025 - Compatibility Fixed"
    }
)

# Ultimate CSS with cutting-edge design
st.markdown("""
<style>
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
    
    /* Global reset and base styles */
    .main {
        font-family: 'Inter', sans-serif;
        color: #000000 !important;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
    }
    
    .main * {
        color: #000000 !important;
    }
    
    /* Ultimate header design */
    .ultimate-header-fixed {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: #ffffff !important;
        padding: 4rem 2rem;
        border-radius: 25px;
        text-align: center;
        margin-bottom: 2rem;
        box-shadow: 
            0 25px 80px rgba(0, 0, 0, 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.2);
        position: relative;
        overflow: hidden;
        border: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    .ultimate-header-fixed::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: 
            radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.4) 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.4) 0%, transparent 50%),
            radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);
        animation: gradientShift 8s ease-in-out infinite;
        pointer-events: none;
    }
    
    @keyframes gradientShift {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.7; }
    }
    
    .ultimate-header-fixed * {
        color: #ffffff !important;
        position: relative;
        z-index: 1;
    }
    
    .ultimate-header-fixed h1 {
        font-size: 4.5rem;
        font-weight: 900;
        margin-bottom: 1rem;
        text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.5);
        background: linear-gradient(45deg, #ffffff, #e0e7ff, #c7d2fe);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        animation: textGlow 3s ease-in-out infinite alternate;
    }
    
    @keyframes textGlow {
        from { text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.5); }
        to { text-shadow: 2px 2px 20px rgba(255, 255, 255, 0.8); }
    }
    
    .ultimate-header-fixed .subtitle {
        font-size: 1.8rem;
        font-weight: 300;
        opacity: 0.95;
        text-shadow: 1px 1px 4px rgba(0, 0, 0, 0.3);
        margin-bottom: 2rem;
    }
    
    .ultimate-header-fixed .features {
        font-size: 1.1rem;
        margin-top: 2rem;
        opacity: 0.9;
        display: flex;
        justify-content: center;
        flex-wrap: wrap;
        gap: 2.5rem;
    }
    
    .ultimate-header-fixed .feature-badge {
        background: rgba(255, 255, 255, 0.2);
        padding: 0.8rem 1.5rem;
        border-radius: 25px;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.3);
        transition: all 0.3s ease;
    }
    
    .ultimate-header-fixed .feature-badge:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    }
    
    /* Ultimate status cards */
    .ultimate-status-card-fixed {
        background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
        padding: 2.5rem;
        border-radius: 20px;
        box-shadow: 
            0 10px 40px rgba(0, 0, 0, 0.1),
            0 1px 0 rgba(255, 255, 255, 0.8) inset;
        border: 1px solid #e2e8f0;
        margin: 1.5rem 0;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
        color: #000000 !important;
    }
    
    .ultimate-status-card-fixed * {
        color: #000000 !important;
    }
    
    .ultimate-status-card-fixed:hover {
        transform: translateY(-10px) scale(1.03);
        box-shadow: 
            0 25px 80px rgba(0, 0, 0, 0.15),
            0 1px 0 rgba(255, 255, 255, 0.8) inset;
    }
    
    .ultimate-status-card-fixed::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 5px;
        background: linear-gradient(90deg, #667eea, #764ba2, #f093fb, #f5576c);
        background-size: 300% 100%;
        animation: gradientMove 3s ease infinite;
    }
    
    @keyframes gradientMove {
        0% { background-position: 0% 50%; }
        50% { background-position: 100% 50%; }
        100% { background-position: 0% 50%; }
    }
    
    .status-online {
        border-left: 5px solid #10b981;
        background: linear-gradient(135deg, #ecfdf5, #d1fae5);
    }
    
    .status-limited {
        border-left: 5px solid #f59e0b;
        background: linear-gradient(135deg, #fffbeb, #fef3c7);
    }
    
    .status-offline {
        border-left: 5px solid #ef4444;
        background: linear-gradient(135deg, #fef2f2, #fecaca);
    }
    
    .status-premium {
        border-left: 5px solid #8b5cf6;
        background: linear-gradient(135deg, #f3e8ff, #e9d5ff);
    }
    
    /* Ultimate metrics display */
    .ultimate-metric-fixed {
        background: linear-gradient(135deg, #ffffff, #f8fafc);
        padding: 2rem;
        border-radius: 20px;
        text-align: center;
        box-shadow: 
            0 8px 32px rgba(0, 0, 0, 0.08),
            0 1px 0 rgba(255, 255, 255, 0.8) inset;
        border: 1px solid #e2e8f0;
        transition: all 0.3s ease;
        color: #000000 !important;
        position: relative;
        overflow: hidden;
    }
    
    .ultimate-metric-fixed * {
        color: #000000 !important;
    }
    
    .ultimate-metric-fixed:hover {
        transform: translateY(-5px);
        box-shadow: 
            0 15px 50px rgba(0, 0, 0, 0.12),
            0 1px 0 rgba(255, 255, 255, 0.8) inset;
    }
    
    .ultimate-metric-fixed::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 3px;
        background: linear-gradient(90deg, #667eea, #764ba2);
    }
    
    .ultimate-metric-value {
        font-size: 3rem;
        font-weight: 900;
        background: linear-gradient(135deg, #667eea, #764ba2, #f093fb);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin-bottom: 0.5rem;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    
    .ultimate-metric-label {
        font-size: 1rem;
        font-weight: 600;
        color: #64748b !important;
        text-transform: uppercase;
        letter-spacing: 1px;
        margin-top: 0.5rem;
    }
    
    /* Ultimate buttons */
    .stButton > button {
        border-radius: 15px;
        border: none;
        padding: 1rem 2rem;
        font-weight: 600;
        font-size: 1rem;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow: 
            0 4px 20px rgba(0, 0, 0, 0.1),
            0 1px 0 rgba(255, 255, 255, 0.2) inset;
        color: #000000 !important;
        position: relative;
        overflow: hidden;
        background: linear-gradient(135deg, #ffffff, #f8fafc);
        border: 1px solid #e2e8f0;
    }
    
    .stButton > button:hover {
        transform: translateY(-3px);
        box-shadow: 
            0 10px 35px rgba(0, 0, 0, 0.2),
            0 1px 0 rgba(255, 255, 255, 0.2) inset;
        background: linear-gradient(135deg, #f8fafc, #e2e8f0);
    }
    
    /* Responsive design */
    @media (max-width: 768px) {
        .ultimate-header-fixed h1 {
            font-size: 3rem;
        }
        
        .ultimate-header-fixed .features {
            flex-direction: column;
            gap: 1rem;
        }
        
        .ultimate-metric-value {
            font-size: 2rem;
        }
    }
</style>
""", unsafe_allow_html=True)

# Ultimate AI Engine with compatibility fixes
class UltimateAIEngineFixed:
    """Compatibility-fixed AI engine for HR with cutting-edge 2025 technologies"""

    def __init__(self):
        self.ollama_url = "http://localhost:11434"
        self.model_name = "qwen2:latest"
        self.conversation_history = []

        # Advanced AI components
        self.sentiment_analyzer = None
        self.emotion_detector = None
        self.bias_detector = None
        self.voice_engine = None
        self.resume_parser = None
        self.predictive_model = None

        # System capabilities (will be updated after safe imports)
        self.capabilities = {
            'ollama_connected': False,
            'voice_synthesis': VOICE_AVAILABLE,
            'sentiment_analysis': SENTIMENT_AVAILABLE,
            'emotion_detection': False,
            'bias_detection': FAIRLEARN_AVAILABLE,
            'resume_parsing': SPACY_AVAILABLE,
            'predictive_analytics': SKLEARN_AVAILABLE,
            'advanced_charts': PLOTLY_AVAILABLE,
            'data_processing': PANDAS_AVAILABLE,
            'nlp_processing': NLTK_AVAILABLE,
            'transformers': False,
            'torch_models': False,
            'fairness_ai': FAIRLEARN_AVAILABLE,
            'gradient_boosting': XGBOOST_AVAILABLE,
            'lightgbm_models': LIGHTGBM_AVAILABLE,
            'cutting_edge_ai': False
        }

        # Initialize components
        self._initialize_components()

    def _initialize_components(self):
        """Initialize all AI components with error handling"""
        try:
            # Check Ollama connection
            self.capabilities['ollama_connected'] = self._check_ollama_connection()

            # Initialize sentiment analysis
            if SENTIMENT_AVAILABLE:
                self.sentiment_analyzer = TextBlob

            # Initialize voice engine
            if VOICE_AVAILABLE:
                self.voice_engine = pyttsx3.init()
                self._setup_voice_engine()

            # Safe PyTorch import after Streamlit initialization
            if safe_import_torch():
                self.capabilities['transformers'] = TRANSFORMERS_AVAILABLE
                self.capabilities['torch_models'] = TORCH_AVAILABLE
                self.capabilities['emotion_detection'] = TRANSFORMERS_AVAILABLE
                self.capabilities['cutting_edge_ai'] = TRANSFORMERS_AVAILABLE and TORCH_AVAILABLE

        except Exception as e:
            st.warning(f"Some AI components unavailable: {e}")

    def _check_ollama_connection(self):
        """Check Ollama connection status"""
        try:
            response = requests.get(f"{self.ollama_url}/api/tags", timeout=5)
            return response.status_code == 200
        except:
            return False

    def _setup_voice_engine(self):
        """Setup advanced voice engine"""
        if self.voice_engine:
            try:
                voices = self.voice_engine.getProperty('voices')
                if voices:
                    # Select best available voice
                    for voice in voices:
                        if 'female' in voice.name.lower() or 'zira' in voice.name.lower():
                            self.voice_engine.setProperty('voice', voice.id)
                            break

                self.voice_engine.setProperty('rate', 180)
                self.voice_engine.setProperty('volume', 0.9)
            except Exception as e:
                st.warning(f"Voice setup warning: {e}")

    def generate_ai_response(self, user_message, candidate_info=None, job_requirements=None):
        """Generate real AI response using Ollama"""
        try:
            # Build context-aware prompt
            context = self._build_interview_context(candidate_info, job_requirements)

            prompt = f"""You are an experienced HR interviewer conducting a professional job interview.

{context}

Conversation so far:
{self._format_conversation_history()}

Candidate just said: "{user_message}"

Provide a professional, engaging follow-up question or response. Keep it conversational and relevant to their background. Limit to 1-2 sentences for voice interviews.

Your response:"""

            # Make request to Ollama
            response = requests.post(
                f"{self.ollama_url}/api/generate",
                json={
                    "model": self.model_name,
                    "prompt": prompt,
                    "stream": False,
                    "options": {
                        "temperature": 0.7,
                        "num_predict": 100,
                        "top_p": 0.9
                    }
                },
                timeout=30
            )

            if response.status_code == 200:
                ai_response = response.json()['response'].strip()

                # Clean up response
                if ai_response.startswith('"') and ai_response.endswith('"'):
                    ai_response = ai_response[1:-1]

                return ai_response
            else:
                return "I understand. Could you tell me more about that experience?"

        except Exception as e:
            st.error(f"AI Error: {e}")
            return "That's interesting. What else would you like to share about your background?"

    def _build_interview_context(self, candidate_info, job_requirements):
        """Build context for AI interview"""
        context = ""

        if candidate_info:
            context += f"Candidate: {candidate_info.get('name', 'Unknown')}\n"
            context += f"Experience: {candidate_info.get('experience_years', 0)} years\n"
            context += f"Skills: {', '.join(candidate_info.get('skills', []))}\n"

        if job_requirements:
            context += f"Role: {job_requirements.get('role', 'Position')}\n"
            context += f"Required Skills: {', '.join(job_requirements.get('required_skills', []))}\n"

        return context

    def _format_conversation_history(self):
        """Format conversation history for context"""
        if not self.conversation_history:
            return "This is the start of the interview."

        formatted = ""
        for entry in self.conversation_history[-3:]:  # Last 3 exchanges
            role = "Interviewer" if entry['role'] == 'ai' else "Candidate"
            formatted += f"{role}: {entry['message']}\n"

        return formatted

    def get_system_status(self):
        """Get comprehensive system status"""
        return {
            'capabilities': self.capabilities,
            'versions': {
                'pandas': PANDAS_VERSION,
                'plotly': PLOTLY_VERSION,
                'sentiment': SENTIMENT_VERSION,
                'nltk': NLTK_VERSION,
                'voice': VOICE_VERSION,
                'sklearn': SKLEARN_VERSION,
                'spacy': SPACY_VERSION,
                'transformers': TRANSFORMERS_VERSION,
                'torch': TORCH_VERSION,
                'fairlearn': FAIRLEARN_VERSION,
                'xgboost': XGBOOST_VERSION,
                'lightgbm': LIGHTGBM_VERSION
            },
            'health_score': sum(self.capabilities.values()) / len(self.capabilities) * 100
        }

# Advanced session state management
def init_advanced_session_state():
    """Initialize advanced session state with all AI features"""
    defaults = {
        # Core system
        'ai_engine_fixed': None,
        'candidates': [],
        'job_requirements': {},
        'interview_active': False,
        'conversation_history': [],
        'selected_candidate': None,

        # Advanced AI features
        'sentiment_analysis_enabled': True,
        'bias_detection_enabled': True,
        'voice_synthesis_enabled': True,
        'emotion_detection_enabled': True,
        'predictive_analytics_enabled': True,

        # System capabilities
        'ai_capabilities': {},
        'torch_imported': False
    }

    for key, value in defaults.items():
        if key not in st.session_state:
            st.session_state[key] = value

def check_ollama_connection():
    """Check Ollama connection status"""
    try:
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            models = response.json().get('models', [])
            return True, [model['name'] for model in models]
        return False, []
    except Exception as e:
        return False, str(e)

def main():
    """Main compatibility-fixed AI HR application"""
    init_advanced_session_state()

    # Initialize AI engine
    if st.session_state.ai_engine_fixed is None:
        with st.spinner("🚀 Initializing Compatibility-Fixed AI Engine..."):
            st.session_state.ai_engine_fixed = UltimateAIEngineFixed()

    # Header
    st.markdown("""
    <div class="ultimate-header-fixed">
        <h1>🚀 Ultimate AI HR 2025</h1>
        <div class="subtitle">Compatibility-Fixed • State-of-the-Art Recruitment Intelligence</div>
        <div class="features">
            <div class="feature-badge">🧠 Advanced AI Models</div>
            <div class="feature-badge">🎤 Voice Processing</div>
            <div class="feature-badge">😊 Emotion Detection</div>
            <div class="feature-badge">⚖️ Bias Detection</div>
            <div class="feature-badge">📊 Predictive Analytics</div>
            <div class="feature-badge">🔧 Compatibility Fixed</div>
        </div>
    </div>
    """, unsafe_allow_html=True)

    # Get system status
    system_status = st.session_state.ai_engine_fixed.get_system_status()

    # Display system status
    st.subheader("🔬 Ultimate AI System Status - Compatibility Fixed")

    col1, col2, col3, col4 = st.columns(4)

    with col1:
        health_score = system_status['health_score']
        status_class = "status-online" if health_score >= 80 else "status-limited" if health_score >= 50 else "status-offline"
        st.markdown(f"""
        <div class="ultimate-status-card-fixed {status_class}">
            <h3>🤖 AI Health</h3>
            <div class="ultimate-metric-value">{health_score:.0f}%</div>
            <p>System Operational</p>
        </div>
        """, unsafe_allow_html=True)

    with col2:
        capabilities = system_status['capabilities']
        online_count = sum(capabilities.values())
        st.markdown(f"""
        <div class="ultimate-status-card-fixed status-premium">
            <h3>⚡ Capabilities</h3>
            <div class="ultimate-metric-value">{online_count}/{len(capabilities)}</div>
            <p>AI Modules Active</p>
        </div>
        """, unsafe_allow_html=True)

    with col3:
        ollama_status = "status-online" if capabilities['ollama_connected'] else "status-offline"
        st.markdown(f"""
        <div class="ultimate-status-card-fixed {ollama_status}">
            <h3>🧠 Ollama AI</h3>
            <p>{'✅ Connected' if capabilities['ollama_connected'] else '❌ Disconnected'}</p>
            <small>{'Real AI Responses' if capabilities['ollama_connected'] else 'Start: ollama serve'}</small>
        </div>
        """, unsafe_allow_html=True)

    with col4:
        torch_status = "status-online" if capabilities['torch_models'] else "status-limited"
        st.markdown(f"""
        <div class="ultimate-status-card-fixed {torch_status}">
            <h3>🔥 PyTorch</h3>
            <p>{'✅ Compatible' if capabilities['torch_models'] else '⚠️ Installing'}</p>
            <small>{'Advanced Models Ready' if capabilities['torch_models'] else 'Compatibility Fixed'}</small>
        </div>
        """, unsafe_allow_html=True)

    # Library versions
    st.subheader("📚 Library Versions - Compatibility Status")
    versions = system_status['versions']

    col1, col2, col3, col4 = st.columns(4)

    with col1:
        st.write(f"**Pandas:** {versions['pandas']}")
        st.write(f"**Plotly:** {versions['plotly']}")
        st.write(f"**Sentiment:** {versions['sentiment']}")

    with col2:
        st.write(f"**NLTK:** {versions['nltk']}")
        st.write(f"**Voice:** {versions['voice']}")
        st.write(f"**Sklearn:** {versions['sklearn']}")

    with col3:
        st.write(f"**spaCy:** {versions['spacy']}")
        st.write(f"**PyTorch:** {versions['torch']}")
        st.write(f"**Transformers:** {versions['transformers']}")

    with col4:
        st.write(f"**Fairlearn:** {versions['fairlearn']}")
        st.write(f"**XGBoost:** {versions['xgboost']}")
        st.write(f"**LightGBM:** {versions['lightgbm']}")

    # Compatibility status
    if capabilities['torch_models']:
        st.success("🎉 **PyTorch Compatibility Fixed!** All advanced AI features are now available.")
    else:
        st.info("🔄 **PyTorch Installing:** Compatibility-fixed version installing. Advanced features will be available shortly.")

    # Test buttons
    st.subheader("🧪 Phase 2 Validation Tests")

    col1, col2, col3 = st.columns(3)

    with col1:
        if st.button("🔗 Test Ollama Connection", type="primary"):
            test_ollama_connection()

    with col2:
        if st.button("🎤 Test Voice Processing"):
            test_voice_processing()

    with col3:
        if st.button("📊 Test AI Analytics"):
            test_ai_analytics()

    st.success("🚀 **Ultimate AI HR 2025 - Compatibility Fixed!** Ready for comprehensive testing and validation.")

def test_ollama_connection():
    """Test Ollama connection and AI responses"""
    with st.spinner("Testing Ollama connection..."):
        is_connected, models_or_error = check_ollama_connection()

        if is_connected:
            st.success(f"✅ **Ollama Connected!** Available models: {len(models_or_error)}")

            # Test AI response
            try:
                response = requests.post(
                    "http://localhost:11434/api/generate",
                    json={
                        "model": "qwen2:latest",
                        "prompt": "Say 'AI HR system test successful' in a professional tone.",
                        "stream": False,
                        "options": {"num_predict": 20}
                    },
                    timeout=10
                )

                if response.status_code == 200:
                    ai_response = response.json()['response'].strip()
                    st.success(f"🤖 **AI Response Test:** {ai_response}")
                else:
                    st.warning("⚠️ Ollama connected but response test failed")

            except Exception as e:
                st.error(f"❌ AI response test error: {e}")
        else:
            st.error(f"❌ **Ollama Disconnected:** {models_or_error}")
            st.info("💡 **Fix:** Run `ollama serve` in terminal to start Ollama")

def test_voice_processing():
    """Test voice processing capabilities"""
    if VOICE_AVAILABLE:
        st.success("✅ **Voice Processing Available!** Speech recognition and synthesis ready.")
        st.info("🎤 Voice features: Speech-to-text, Text-to-speech, Voice analysis")
    else:
        st.warning("⚠️ **Voice Processing Limited:** Install speechrecognition and pyttsx3 for full voice features")

def test_ai_analytics():
    """Test AI analytics capabilities"""
    analytics_status = []

    if SENTIMENT_AVAILABLE:
        analytics_status.append("✅ Sentiment Analysis")
    else:
        analytics_status.append("❌ Sentiment Analysis")

    if SKLEARN_AVAILABLE:
        analytics_status.append("✅ Predictive Models")
    else:
        analytics_status.append("❌ Predictive Models")

    if FAIRLEARN_AVAILABLE:
        analytics_status.append("✅ Bias Detection")
    else:
        analytics_status.append("❌ Bias Detection")

    if PLOTLY_AVAILABLE:
        analytics_status.append("✅ Advanced Charts")
    else:
        analytics_status.append("❌ Advanced Charts")

    st.write("**AI Analytics Status:**")
    for status in analytics_status:
        st.write(status)

if __name__ == "__main__":
    main()
