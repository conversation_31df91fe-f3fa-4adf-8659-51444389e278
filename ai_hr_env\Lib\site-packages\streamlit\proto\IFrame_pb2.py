# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: streamlit/proto/IFrame.proto
# Protobuf Python Version: 5.26.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1cstreamlit/proto/IFrame.proto\"\x9c\x01\n\x06IFrame\x12\r\n\x03src\x18\x01 \x01(\tH\x00\x12\x10\n\x06srcdoc\x18\x02 \x01(\tH\x00\x12\r\n\x05width\x18\x03 \x01(\x02\x12\x11\n\thas_width\x18\x04 \x01(\x08\x12\x0e\n\x06height\x18\x05 \x01(\x02\x12\x11\n\tscrolling\x18\x07 \x01(\x08\x12\x16\n\ttab_index\x18\x08 \x01(\x05H\x01\x88\x01\x01\x42\x06\n\x04typeB\x0c\n\n_tab_indexB+\n\x1c\x63om.snowflake.apps.streamlitB\x0bIFrameProtob\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'streamlit.proto.IFrame_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\034com.snowflake.apps.streamlitB\013IFrameProto'
  _globals['_IFRAME']._serialized_start=33
  _globals['_IFRAME']._serialized_end=189
# @@protoc_insertion_point(module_scope)
