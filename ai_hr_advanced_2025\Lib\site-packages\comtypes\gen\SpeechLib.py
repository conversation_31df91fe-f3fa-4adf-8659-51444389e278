from enum import IntFlag

import comtypes.gen._C866CA3A_32F7_11D2_9602_00C04F8EE628_0_5_4 as __wrapper_module__
from comtypes.gen._C866CA3A_32F7_11D2_9602_00C04F8EE628_0_5_4 import (
    DISPID_SGRSTText, ISpeechRecoResultTimes, SRTEmulated,
    DISPID_SRGId, DISPID_SPEEngineConfidence,
    ISpStreamFormatConverter, SpeechAllElements, ISpeechAudio,
    SDA_One_Trailing_Space, SRTAutopause, DISPID_SRGIsPronounceable,
    DISPID_SGRSTPropertyId, DISPID_SASState, eLEXTYPE_PRIVATE1,
    SAFTCCITT_uLaw_22kHzStereo, DISPID_SPERetainedSizeBytes,
    SP_VISEME_21, DISPID_SVEPhoneme, DISPID_SPIGetDisplayAttributes,
    DISPID_SLRemovePronunciation, DISPID_SGRsFindRule,
    DISPID_SPAsCount, SpeechRecoProfileProperties,
    SAFT11kHz8BitStereo, SPEI_SENTENCE_BOUNDARY, DISPID_SPRulesCount,
    DISPID_SASCurrentDevicePosition, ISpNotifyTranslator,
    eWORDTYPE_DELETED, DISPID_SOTCategory, CoClass,
    SAFT24kHz8BitStereo, SREPropertyNumChange, WAVEFORMATEX,
    SVEAudioLevel, SPBO_PAUSE, SPSNotOverriden, SVEEndInputStream,
    DISPID_SOTGetAttribute, DISPID_SPIAudioSizeBytes,
    SPINTERFERENCE_NONE, ISpeechLexiconWord, DISPID_SRRTimes,
    DISPID_SPAs_NewEnum, SP_VISEME_1, SAFTCCITT_uLaw_11kHzStereo,
    DISPID_SDKSetLongValue, SAFT48kHz16BitMono,
    DISPID_SRCEInterference, DISPID_SGRSAddSpecialTransition,
    SPPS_RESERVED4, SPFM_NUM_MODES, SRAONone, SAFTADPCM_11kHzStereo,
    DISPID_SRRSpeakAudio, SpeechAddRemoveWord,
    DISPID_SRRTOffsetFromStart, SPSHT_NotOverriden,
    DISPID_SPIAudioStreamPosition, SPDKL_CurrentConfig,
    ISpeechRecoResult2, SAFT8kHz8BitMono, DISPID_SASFreeBufferSpace,
    _check_version, SVP_17, DISPID_SVIsUISupported, SPEI_UNDEFINED,
    SVP_10, DISPID_SPIElements, DISPID_SPRFirstElement, SRSEDone,
    SP_VISEME_6, SRATopLevel, ISpeechRecognizer, eLEXTYPE_PRIVATE18,
    SPPS_Interjection, SVPAlert, SPFM_CREATE_ALWAYS, SPAR_High,
    SVSFIsXML, DISPID_SGRsCommitAndSave,
    DISPID_SVAllowAudioOuputFormatChangesOnNextSet, SPAS_PAUSE,
    SPRS_ACTIVE_WITH_AUTO_PAUSE, DISPID_SAEventHandle,
    DISPID_SPRuleId, ISpStream, SPINTERFERENCE_LATENCY_WARNING,
    SPSModifier, DISPIDSPTSI_SelectionLength, DISPID_SPIAudioSizeTime,
    DISPID_SRGRecoContext, SPEI_PHONEME, ISpeechPhraseElement,
    DISPID_SVSInputSentencePosition, eLEXTYPE_PRIVATE8,
    SAFTCCITT_uLaw_8kHzStereo, DISPID_SVEVoiceChange,
    ISpeechPhraseReplacements, eLEXTYPE_PRIVATE4,
    DISPID_SPEDisplayText, SAFTADPCM_44kHzMono,
    SpeechCategoryAppLexicons, SAFT24kHz8BitMono,
    DISPID_SPIReplacements, SVSFlagsAsync, SPSVerb, DISPID_SMSGetData,
    SPEI_VISEME, SVSFUnusedFlags, SPEI_RESERVED3, SREAllEvents,
    SVEStartInputStream, SAFTCCITT_uLaw_8kHzMono,
    DISPID_SPRuleEngineConfidence, SPSHORTCUTPAIRLIST,
    ISpXMLRecoResult, DISPID_SRRGetXMLResult,
    DISPID_SPRDisplayAttributes, SREStateChange, ISpeechFileStream,
    DISPID_SRSClsidEngine, SPSMF_SAPI_PROPERTIES, SPAS_RUN,
    SAFT16kHz8BitStereo, ISpeechPhraseProperties,
    SPINTERFERENCE_LATENCY_TRUNCATE_END, ISpeechGrammarRule,
    SAFT12kHz16BitMono, DISPID_SLWs_NewEnum, SLODynamic,
    SpObjectTokenCategory, eLEXTYPE_PRIVATE17, DISPID_SRCEEndStream,
    DISPID_SRCResume, ISpeechObjectTokenCategory, SpSharedRecognizer,
    ISpSerializeState, SPRULE, DISPID_SPPsCount, SPGS_ENABLED,
    DISPID_SVStatus, SAFT48kHz8BitStereo, DISPID_SVEventInterests,
    DISPID_SDKSetBinaryValue, SPVOICESTATUS, DISPID_SGRSTsItem,
    DISPID_SGRsCommit, SVP_11, VARIANT_BOOL, ISpObjectToken,
    SVSFParseSapi, DISPID_SPILanguageId, SPEI_MIN_TTS,
    DISPID_SGRSTs_NewEnum, DISPID_SPAStartElementInResult, SRTReSent,
    SSSPTRelativeToEnd, ISpeechPhraseAlternates, SFTInput,
    DISPID_SLAddPronunciation, SAFTGSM610_44kHzMono,
    DISPID_SPIRetainedSizeBytes, eLEXTYPE_PRIVATE20,
    SPRECOGNIZERSTATUS, SPGS_EXCLUSIVE, SpeechAudioVolume,
    DISPID_SVAudioOutput, SVEViseme, DISPID_SGRSTPropertyValue,
    DISPID_SGRSTsCount, SVSFIsFilename, SPEI_SR_RETAINEDAUDIO,
    helpstring, SPWORDPRONUNCIATION, SAFTNonStandardFormat, SVP_15,
    DISPID_SASetState, SVP_12, DISPID_SWFEChannels,
    DISPID_SRCSetAdaptationData, SP_VISEME_10, DISPID_SRRAudioFormat,
    DISPID_SVRate, SPLO_STATIC, Speech_Max_Pron_Length,
    DISPIDSPTSI_ActiveLength, ISpGrammarBuilder,
    DISPID_SRGetRecognizers, SpObjectToken, eLEXTYPE_PRIVATE16,
    SPEI_TTS_BOOKMARK, DISPID_SLGetPronunciations,
    DISPID_SOTGetDescription, SAFTCCITT_ALaw_8kHzStereo,
    SGRSTTWildcard, DISPID_SRCESoundEnd, ISpRecoCategory,
    DISPID_SGRSTPropertyName, SREStreamStart, SPCT_DICTATION,
    SPEI_MAX_SR, LONG_PTR, SLOStatic, SAFT12kHz16BitStereo,
    eLEXTYPE_PRIVATE9, SpeechGrammarTagWildcard, SPGS_DISABLED,
    DISPID_SRRSaveToMemory, DISPID_SRCERequestUI, SECNormalConfidence,
    DISPID_SRSetPropertyString, SPAR_Low, ISpeechPhraseRules,
    DISPID_SDKDeleteKey, DISPID_SRSSupportedLanguages,
    DISPID_SRGRules, ISpeechPhraseElements, SPPS_Function, _FILETIME,
    SAFT44kHz16BitMono, DISPID_SRRTLength,
    DISPID_SRCEFalseRecognition, DISPID_SRCVoicePurgeEvent,
    SAFT24kHz16BitStereo, DISPID_SOTCreateInstance,
    DISPID_SDKSetStringValue, DISPID_SPPChildren,
    SRSInactiveWithPurge, DISPID_SPIGetText,
    DISPID_SABIMinNotification, eLEXTYPE_USER,
    DISPID_SRCCreateGrammar, DISPID_SPEsCount, SP_VISEME_20,
    SpeechCategoryAudioIn, DISPID_SRGReset, SpPhoneConverter,
    SRERequestUI, DISPID_SLAddPronunciationByPhoneIds,
    IEnumSpObjectTokens, SPWP_UNKNOWN_WORD_PRONOUNCEABLE,
    SSSPTRelativeToCurrentPosition, SGLexical, eLEXTYPE_PRIVATE7,
    SPWT_PRONUNCIATION, DISPID_SGRAttributes, SPFM_OPEN_READONLY,
    SDA_No_Trailing_Space, eWORDTYPE_ADDED, eLEXTYPE_USER_SHORTCUT,
    SPTEXTSELECTIONINFO, DISPID_SPANumberOfElementsInResult,
    DISPID_SPPFirstElement, ISpPhrase, SPCT_COMMAND, IServiceProvider,
    IEnumString, ISpStreamFormat, SAFT8kHz16BitStereo,
    SPEI_PROPERTY_STRING_CHANGE, SAFT11kHz8BitMono,
    DISPID_SGRSTWeight, SDTReplacement, DISPID_SVEWord, SPVPRI_OVER,
    SPSHT_Unknown, SpeechDictationTopicSpelling, DISPID_SLGetWords,
    DISPID_SRCreateRecoContext, ISpeechRecoGrammar,
    DISPID_SRAudioInputStream, SPRST_NUM_STATES, DISPID_SPEs_NewEnum,
    DISPID_SDKEnumKeys, ISpeechRecognizerStatus,
    DISPID_SRCERecognizerStateChange, SpPhoneticAlphabetConverter,
    DISPID_SVEStreamStart, DISPID_SOTCId, SREPrivate,
    DISPID_SPCPhoneToId, SPAS_CLOSED, DISPID_SDKEnumValues,
    ISpLexicon, ISpeechDataKey, DISPID_SGRAddState, SpWaveFormatEx,
    SVP_3, SpStreamFormatConverter, DISPIDSPTSI_SelectionOffset,
    SpLexicon, SASClosed, ISpNotifySource, tagSPTEXTSELECTIONINFO,
    ISpeechObjectTokens, ISpRecoResult, DISPID_SPACommit,
    SPCS_ENABLED, WSTRING, SPEI_MIN_SR, eLEXTYPE_APP,
    ISpeechAudioFormat, SDKLCurrentConfig, DISPID_SVGetVoices,
    SPEI_ADAPTATION, DISPID_SOTRemoveStorageFileName,
    SPEI_PHRASE_START, SP_VISEME_9, DISPID_SOTCSetId,
    DISPID_SPERequiredConfidence, ISpRecoContext2, SSFMCreate,
    SDKLCurrentUser, SPSInterjection, ISpeechMemoryStream,
    SAFT22kHz8BitStereo, wireHWND, DISPID_SVAlertBoundary, SINoise,
    SVP_9, SPEI_RESERVED2, STSF_AppData, STCInprocHandler,
    DISPID_SRSNumberOfActiveRules, ISpeechLexiconPronunciations,
    SPINTERFERENCE_NOSIGNAL, SpeechCategoryRecoProfiles,
    SPWP_UNKNOWN_WORD_UNPRONOUNCEABLE, eLEXTYPE_RESERVED8,
    ISpeechResourceLoader, SRAORetainAudio, ISpeechPhraseProperty,
    SP_VISEME_2, SRAInterpreter, SAFT32kHz16BitMono,
    DISPID_SRGCmdLoadFromFile, SpeechPropertyResourceUsage,
    ISpeechXMLRecoResult, SVPOver, IStream, SSFMOpenReadWrite,
    DISPID_SLRemovePronunciationByPhoneIds, ISpeechLexicon,
    SpeechPropertyLowConfidenceThreshold, SAFT32kHz8BitMono,
    DISPID_SRCEAudioLevel, SPDKL_DefaultLocation, SRARoot,
    DISPID_SPIEnginePrivateData, SP_VISEME_19, SPSFunction,
    ISpNotifySink, SpeechRegistryUserRoot,
    DISPID_SVSInputWordPosition, SPCT_SUB_COMMAND, SGDSActive,
    DISPID_SLPsItem, Speech_Max_Word_Length, eLEXTYPE_PRIVATE12,
    SVPNormal, DISPID_SVSkip, SECHighConfidence,
    DISPID_SVEEnginePrivate, SAFT12kHz8BitStereo, SPWF_INPUT,
    DISPID_SPEAudioTimeOffset, DISPID_SPEActualConfidence,
    ISpeechMMSysAudio, ISpeechAudioBufferInfo,
    DISPID_SRCRetainedAudio, ISequentialStream, DISPID_SLWLangId,
    ISpRecognizer2, SAFTCCITT_uLaw_22kHzMono, Speech_Default_Weight,
    dispid, SDTDisplayText, DISPID_SPPs_NewEnum,
    SpeechPropertyHighConfidenceThreshold,
    SPINTERFERENCE_LATENCY_TRUNCATE_BEGIN, SPSSuppressWord,
    DISPID_SVSLastBookmarkId, SRERecognition,
    DISPID_SRRSetTextFeedback, DISPID_SPRuleFirstElement,
    DISPID_SLGetGenerationChange, SAFT16kHz16BitStereo,
    DISPID_SPRulesItem, SAFT8kHz8BitStereo, DISPID_SPPValue,
    SPPS_RESERVED3, SPEI_HYPOTHESIS, SITooQuiet, SAFTGSM610_22kHzMono,
    DISPID_SVSPhonemeId, DISPID_SPRuleConfidence,
    DISPID_SLGenerationId, ISpeechBaseStream, SPEI_RESERVED6,
    DISPID_SABufferNotifySize, SECFEmulateResult,
    SAFTCCITT_ALaw_8kHzMono, SWTAdded, SP_VISEME_12,
    SWPUnknownWordPronounceable, SVESentenceBoundary, SECFIgnoreCase,
    SVP_21, DISPID_SABIEventBias, SGRSTTWord, DISPID_SRState,
    DISPID_SAVolume, SP_VISEME_15, SPAO_RETAIN_AUDIO,
    DISPID_SMSSetData, DISPID_SRCRequestedUIType, SPAUDIOSTATUS,
    SPPS_Verb, DISPID_SLPSymbolic, SP_VISEME_7, SPDKL_CurrentUser,
    DISPID_SRRecognizer, DISPID_SPRsItem, SWPKnownWordPronounceable,
    SAFT11kHz16BitMono, DISPID_SRRAlternates, tagSPPROPERTYINFO,
    DISPID_SBSRead, SAFTGSM610_8kHzMono, DISPID_SRCEBookmark,
    DISPID_SPEDisplayAttributes, DISPID_SVSInputSentenceLength,
    SPVPRI_ALERT, DISPID_SGRsCount, DISPID_SPRuleChildren,
    SREBookmark, DISPID_SRCEAdaptation, SPPHRASE,
    SpeechCategoryVoices, SRESoundEnd, eLEXTYPE_RESERVED6,
    SAFTTrueSpeech_8kHz1BitMono, SGDSInactive, DISPID_SPAPhraseInfo,
    DISPID_SGRSAddRuleTransition, DISPID_SRSCurrentStreamPosition,
    ISpeechGrammarRuleStateTransitions, DISPID_SOTCEnumerateTokens,
    SPPHRASEREPLACEMENT, SPEI_FALSE_RECOGNITION,
    SAFTCCITT_ALaw_22kHzStereo, SpSharedRecoContext, DISPID_SBSWrite,
    DISPID_SGRSAddWordTransition, STSF_LocalAppData, SPSUnknown,
    SAFTADPCM_8kHzMono, SAFTADPCM_22kHzStereo, DISPID_SABufferInfo,
    SVP_0, SP_VISEME_4, DISPID_SOTIsUISupported, SVEVoiceChange,
    SITooLoud, DISPID_SRAllowVoiceFormatMatchingOnNextSet,
    DISPID_SRCERecognitionForOtherContext, STCRemoteServer,
    SpeechPropertyComplexResponseSpeed, SAFTCCITT_uLaw_44kHzMono,
    DISPID_SPIStartTime, SPEI_RECO_OTHER_CONTEXT,
    SPSMF_SRGS_SAPIPROPERTIES, SPINTERFERENCE_TOOFAST, SpMMAudioOut,
    SpResourceManager, SVP_14, ISpVoice, DISPID_SAFType,
    DISPID_SVAudioOutputStream, DISPMETHOD, SAFT32kHz8BitStereo,
    DISPID_SGRName, SP_VISEME_5, SVP_7, DISPID_SGRId,
    DISPID_SRGDictationSetState, ISpeechRecoContext, SAFTDefault,
    DISPID_SRGSetWordSequenceData, DISPID_SRStatus,
    SAFTGSM610_11kHzMono, ISpeechGrammarRuleState,
    SpeechGrammarTagDictation, SpeechCategoryRecognizers,
    SPPS_RESERVED1, SP_VISEME_11, SRTExtendableParse,
    SpeechVoiceCategoryTTSRate, SPDKL_LocalMachine,
    DISPID_SWFEBlockAlign, SREHypothesis, SVEAllEvents,
    DISPID_SRCRetainedAudioFormat, SRAImport, DISPID_SRGCommit,
    _LARGE_INTEGER, DISPID_SPIProperties,
    DISPID_SRCEPropertyStringChange, SpNotifyTranslator,
    SPRST_INACTIVE, SpeechGrammarTagUnlimitedDictation,
    DISPID_SLPsCount, DISPID_SOTs_NewEnum, SREInterference, SPSNoun,
    DISPID_SFSClose, SpeechTokenKeyFiles, SPEI_SR_AUDIO_LEVEL,
    SVEBookmark, _ISpeechRecoContextEvents, DISPID_SLWType, SVP_6,
    eLEXTYPE_RESERVED4, DISPID_SGRsDynamic, _ISpeechVoiceEvents,
    DISPID_SOTSetId, SVP_8, DISPID_SRGCmdLoadFromMemory, SPAS_STOP,
    SPEI_PROPERTY_NUM_CHANGE, SPPS_Noncontent, SAFTNoAssignedFormat,
    SFTSREngine, SAFT48kHz8BitMono, SRTSMLTimeout, STSF_FlagCreate,
    __MIDL___MIDL_itf_sapi_0000_0020_0002,
    ISpeechTextSelectionInformation, STCInprocServer,
    eLEXTYPE_PRIVATE15, SAFTCCITT_ALaw_11kHzMono,
    SREPropertyStringChange, SPRST_INACTIVE_WITH_PURGE,
    DISPID_SVEAudioLevel, DISPID_SRRGetXMLErrorInfo, SPRST_ACTIVE,
    SGDSActiveWithAutoPause, DISPID_SWFEExtraData, SITooSlow,
    SPPS_Noun, SPSHORTCUTPAIR, SPPHRASEPROPERTY, ISpeechRecoResult,
    SAFT32kHz16BitStereo, SpNullPhoneConverter, eLEXTYPE_RESERVED7,
    SRSEIsSpeaking, DISPID_SVVoice, DISPID_SBSFormat,
    DISPID_SWFEAvgBytesPerSec, SAFTADPCM_11kHzMono,
    STSF_CommonAppData, DISPID_SRProfile, SGRSTTRule, GUID, SASStop,
    SpeechMicTraining, SREPhraseStart, DISPID_SMSAMMHandle,
    ISpeechCustomStream, SpMMAudioIn, DISPID_SPCLangId, UINT_PTR,
    DISPID_SRGCmdSetRuleIdState, ISpeechObjectToken,
    DISPID_SPRuleNumberOfElements, SAFT44kHz8BitStereo, IUnknown,
    SP_VISEME_0, DISPID_SVResume, SBOPause, SAFT11kHz16BitStereo,
    ISpeechVoiceStatus, SDTAll, DISPID_SLPPartOfSpeech,
    SSFMOpenForRead, SAFTExtendedAudioFormat,
    DISPID_SAFGetWaveFormatEx, DISPID_SVPause,
    DISPID_SRCEPropertyNumberChange, DISPID_SRSCurrentStreamNumber,
    DISPID_SRRAudio, SPINTERFERENCE_NOISE, SpeechAudioFormatGUIDWave,
    SGSDisabled, DISPID_SOTsItem, SpeechVoiceSkipTypeSentence,
    SPEI_RESERVED5, SAFT22kHz16BitStereo, _RemotableHandle,
    ISpRecoContext, SVP_20, DISPID_SABIBufferSize, DISPID_SPRsCount,
    eLEXTYPE_RESERVED10, SDTAlternates, SPEI_START_SR_STREAM,
    SPPS_SuppressWord, DISPID_SRGetFormat, ISpAudio, DISPID_SPPName,
    ISpResourceManager, SpMemoryStream, DISPID_SPEAudioSizeTime,
    SGDisplay, SDTLexicalForm, SAFTCCITT_ALaw_44kHzStereo,
    SVSFIsNotXML, DISPID_SRIsShared, DISPID_SLWWord,
    eLEXTYPE_PRIVATE14, DISPID_SPEsItem, ISpDataKey, SREAudioLevel,
    SAFTCCITT_ALaw_44kHzMono, SGSEnabled, SPWORDPRONUNCIATIONLIST,
    SRADynamic, SpeechAudioProperties, SPEI_RECOGNITION,
    SpMMAudioEnum, SAFTCCITT_uLaw_11kHzMono, DISPID_SPISaveToMemory,
    DISPID_SRRTStreamTime, DISPID_SGRsItem, SPCT_SUB_DICTATION,
    DISPID_SVPriority, SGRSTTDictation, Speech_StreamPos_Asap,
    SDKLDefaultLocation, SVP_5, ISpProperties, ISpShortcut,
    SPSMF_SRGS_SEMANTICINTERPRETATION_MS, DISPID_SVVolume,
    DISPID_SVGetAudioOutputs, DISPID_SRAudioInput,
    IInternetSecurityManager, DISPID_SPRs_NewEnum,
    SAFTADPCM_8kHzStereo, DISPID_SPRules_NewEnum, SPFM_OPEN_READWRITE,
    DISPID_SGRInitialState, DISPID_SASCurrentSeekPosition,
    DISPID_SGRSRule, SRESoundStart, ISpMMSysAudio, SPSERIALIZEDPHRASE,
    DISPID_SVGetProfiles, DISPID_SLPs_NewEnum, SECFIgnoreKanaType,
    DISPID_SPPsItem, SDTPronunciation, DISPID_SVSpeakStream,
    SDKLLocalMachine, SPEI_RESERVED1, SPEI_END_INPUT_STREAM,
    DISPID_SVDisplayUI, ISpPhoneConverter, SPXRO_SML, SECFIgnoreWidth,
    DISPID_SPELexicalForm, SP_VISEME_8, DISPID_SRGetPropertyNumber,
    SDA_Two_Trailing_Spaces, SAFT16kHz8BitMono,
    DISPID_SRGCmdSetRuleState, SAFTADPCM_44kHzStereo,
    SpeechCategoryPhoneConverters, SP_VISEME_3, SPRS_ACTIVE,
    IInternetSecurityMgrSite, DISPID_SRGDictationUnload, Library,
    DISPIDSPTSI_ActiveOffset, SPSERIALIZEDRESULT, SVEPrivate,
    DISPID_SPEPronunciation, DISPID_SPPConfidence, eLEXTYPE_PRIVATE5,
    DISPID_SGRSTRule, SGPronounciation, DISPID_SVSRunningState,
    ISpRecoGrammar, SWTDeleted, SPEI_SOUND_START, SPRECOCONTEXTSTATUS,
    SP_VISEME_14, SPEI_END_SR_STREAM, SRCS_Enabled, DISPID_SBSSeek,
    ISpPhraseAlt, SREAdaptation, ISpeechPhraseInfoBuilder,
    ISpeechPhraseReplacement, SPEI_SR_PRIVATE, SpeechEngineProperties,
    SPPS_LMA, ISpeechLexiconWords, typelib_path, SPBINARYGRAMMAR,
    SVSFNLPSpeakPunc, eLEXTYPE_LETTERTOSOUND, SPPS_NotOverriden,
    SVP_4, DISPID_SOTDataKey, SVP_16,
    DISPID_SPPBRestorePhraseFromMemory, DISPID_SLPLangId,
    DISPID_SPRNumberOfElements, DISPID_SGRClear, SPPHRASEELEMENT,
    SINoSignal, SPPROPERTYINFO, SpeechRegistryLocalMachineRoot,
    SPBO_NONE, DISPID_SPAsItem, DISPID_SCSBaseStream,
    DISPID_SPEAudioStreamOffset, __MIDL_IWinTypes_0009, SLTUser,
    DISPID_SRCEStartStream, SVP_2, SVSFParseSsml, SINone,
    SPPHRASERULE, SECFDefault, DISPID_SWFEBitsPerSample,
    DISPID_SRCEHypothesis, SPEI_TTS_PRIVATE, SpeechTokenKeyUI,
    SECLowConfidence, SpInProcRecoContext, HRESULT,
    SPRS_ACTIVE_USER_DELIMITED, SPEI_WORD_BOUNDARY,
    DISPID_SASNonBlockingIO, SPCT_SLEEP, SDTAudio, SAFT44kHz8BitMono,
    SpeechAudioFormatGUIDText, _ULARGE_INTEGER, DISPID_SVEViseme,
    SP_VISEME_17, ISpeechPhraseRule, SP_VISEME_16,
    DISPID_SPCIdToPhone, DISPID_SRCBookmark, SPEI_SR_BOOKMARK,
    SP_VISEME_13, SPSMF_SRGS_SEMANTICINTERPRETATION_W3C,
    DISPID_SOTCDefault, ULONG_PTR, SAFT48kHz16BitStereo,
    SPRS_INACTIVE, SGSExclusive, SVSFPersistXML,
    DISPID_SRCEventInterests, DISPID_SDKCreateKey, SpeechUserTraining,
    SpPhraseInfoBuilder, DISPID_SVSLastResult, SP_VISEME_18,
    ISpeechPhraseAlternate, DISPID_SRDisplayUI,
    SAFTCCITT_ALaw_22kHzMono, SREStreamEnd, DISPID_SOTId,
    DISPID_SPPNumberOfElements, DISPID_SRIsUISupported,
    SAFT44kHz16BitStereo, SpeechCategoryAudioOut, SVSFDefault,
    DISPID_SDKGetBinaryValue, SPEI_SOUND_END,
    DISPID_SPERetainedStreamOffset,
    DISPID_SRGCmdLoadFromProprietaryGrammar, DISPID_SLPPhoneIds,
    SVP_18, SPWT_DISPLAY, DISPID_SRSAudioStatus, eLEXTYPE_PRIVATE13,
    SPSHT_EMAIL, SBONone, DISPID_SRCAudioInInterferenceStatus,
    DISPID_SPEAudioSizeBytes, DISPID_SOTRemove, SRADefaultToActive,
    SAFT22kHz16BitMono, DISPID_SVSpeak, SPEVENTSOURCEINFO,
    SPRST_ACTIVE_ALWAYS, DISPID_SPRuleParent, SGLexicalNoSpecialChars,
    DISPID_SVEStreamEnd, DISPID_SRGSetTextSelection, SpVoice,
    SpCompressedLexicon, SSSPTRelativeToStart,
    DISPID_SOTGetStorageFileName, SGRSTTTextBuffer,
    SPINTERFERENCE_TOOSLOW, eLEXTYPE_RESERVED9, ISpRecognizer3,
    ISpRecognizer, SRSActiveAlways, SPVPRI_NORMAL,
    SPWT_LEXICAL_NO_SPECIAL_CHARS, SVSFPurgeBeforeSpeak,
    SAFT12kHz8BitMono, DISPID_SRRRecoContext,
    ISpeechLexiconPronunciation, eLEXTYPE_PRIVATE19, SPWT_LEXICAL,
    SRERecoOtherContext, SPAR_Medium, SPCS_DISABLED, SRSActive,
    DISPID_SVWaitUntilDone, SpCustomStream, DISPID_SRCVoice,
    SpShortcut, BSTR, DISPID_SWFEFormatTag, ISpRecoGrammar2,
    SREFalseRecognition, SpUnCompressedLexicon, SpFileStream,
    SAFT24kHz16BitMono, SVEPhoneme, DISPID_SWFESamplesPerSec,
    DISPID_SVSCurrentStreamNumber, SPEI_MAX_TTS,
    SPINTERFERENCE_TOOLOUD, SITooFast, SVSFParseMask,
    DISPID_SRRPhraseInfo, SDTRule, DISPID_SVSLastBookmark,
    SSTTDictation, SGRSTTEpsilon, ISpeechPhraseInfo,
    DISPID_SLWPronunciations, DISPID_SPIEngineId,
    SpeechPropertyResponseSpeed, DISPID_SPPParent,
    DISPID_SRRDiscardResultInfo, DISPID_SREmulateRecognition,
    SVSFParseAutodetect, SPEI_ACTIVE_CATEGORY_CHANGED,
    __MIDL___MIDL_itf_sapi_0000_0020_0001, DISPID_SPIGrammarId,
    SPEI_INTERFERENCE, SLTApp, SPFM_CREATE, SSTTWildcard, SVF_None,
    DISPID_SVSLastStreamNumberQueued, DISPID_SRCEPhraseStart,
    SRSInactive, DISPID_SRCPause, ISpeechGrammarRuleStateTransition,
    STCAll, ISpObjectWithToken, SASPause, SPWORD, DISPID_SGRSTType,
    SpeechTokenValueCLSID, SAFTText, SPAUDIOBUFFERINFO,
    eLEXTYPE_VENDORLEXICON, SPEI_START_INPUT_STREAM,
    SPEI_RECO_STATE_CHANGE, DISPID_SRGDictationLoad,
    SPINTERFERENCE_TOOQUIET, DISPID_SOTDisplayUI, SPAR_Unknown,
    DISPID_SADefaultFormat, SASRun, DISPID_SOTCGetDataKey,
    DISPID_SFSOpen, DISPID_SRCCmdMaxAlternates, tagSTATSTG,
    ISpEventSink, SPRECORESULTTIMES, eLEXTYPE_PRIVATE3,
    DISPID_SVEBookmark, DISPID_SLPType, SPSMF_UPS,
    DISPID_SRSetPropertyNumber, SVEWordBoundary, SVSFNLPMask,
    SAFT8kHz16BitMono, DISPID_SRCRecognizer, DISPID_SVSVisemeId,
    SpeechTokenIdUserLexicon, DISPID_SPRuleName, SPWF_SRENGINE,
    DISPID_SGRSTNextState, SRTStandard,
    DISPID_SVSyncronousSpeakTimeout, DISPID_SRCCreateResultFromMemory,
    DISPID_SRGCmdLoadFromObject, SECFNoSpecialChars, ISpEventSource,
    SAFT22kHz8BitMono, DISPID_SGRAddResource, SVP_13,
    Speech_StreamPos_RealTime, ISpeechWaveFormatEx, DISPID_SLWsItem,
    DISPID_SRAllowAudioInputFormatChangesOnNextSet, DISPID_SLWsCount,
    ISpeechVoice, SAFT16kHz16BitMono, _lcid,
    SAFTCCITT_ALaw_11kHzStereo, ISpeechGrammarRules,
    DISPID_SRCESoundStart, SpeechPropertyNormalConfidenceThreshold,
    SpInprocRecognizer, SWPUnknownWordUnpronounceable,
    SSFMCreateForWrite, DISPID_SPARecoResult,
    DISPID_SVSInputWordLength, SVSFVoiceMask, SPAO_NONE, SDTProperty,
    DISPID_SGRSTransitions, SPXRO_Alternates_SML,
    DISPID_SRRTTickCount, SVP_19, eLEXTYPE_PRIVATE11, SPBO_AHEAD,
    DISPID_SRCState, DISPID_SRGCmdLoadFromResource, SPSLMA,
    DISPID_SPRText, STCLocalServer, DISPID_SOTsCount,
    SPEI_VOICE_CHANGE, SDA_Consume_Leading_Spaces, SPWORDLIST,
    SpTextSelectionInformation, DISPID_SPPId, VARIANT,
    ISpeechAudioStatus, DISPID_SDKGetlongValue,
    SAFTCCITT_uLaw_44kHzStereo, SPLO_DYNAMIC, DISPID_SRCERecognition,
    DISPID_SAStatus, SpStream, SpeechTokenKeyAttributes,
    DISPID_SPIRule, SPEI_REQUEST_UI, SpeechPropertyAdaptationOn,
    SPSHT_OTHER, SPSEMANTICERRORINFO, DISPID_SDKGetStringValue,
    DISPID_SRCEEnginePrivate, DISPID_SOTMatchesAttributes,
    SPBO_TIME_UNITS, SPWP_KNOWN_WORD_PRONOUNCEABLE, SVP_1,
    DISPID_SPPEngineConfidence, SSTTTextBuffer, eLEXTYPE_MORPHOLOGY,
    eLEXTYPE_PRIVATE6, DISPID_SGRsAdd, SPPS_Unknown, SRCS_Disabled,
    DISPID_SAFSetWaveFormatEx, ISpeechPhoneConverter,
    SPEI_TTS_AUDIO_LEVEL, DISPID_SRGState, DISPID_SVESentenceBoundary,
    eLEXTYPE_PRIVATE2, DISPID_SGRs_NewEnum, ISpObjectTokenCategory,
    eLEXTYPE_PRIVATE10, DISPID_SDKOpenKey, SPEVENT,
    DISPID_SMSADeviceId, SpAudioFormat, ISpPhoneticAlphabetSelection,
    SPPS_RESERVED2, DISPID_SVSpeakCompleteEvent, DISPID_SMSALineId,
    ISpPhoneticAlphabetConverter, DISPID_SDKDeleteValue, COMMETHOD,
    SAFTADPCM_22kHzMono, SPPS_Modifier, SVF_Stressed,
    ISpeechRecoResultDispatch, SGDSActiveUserDelimited,
    DISPID_SAFGuid, SRAExport, DISPID_SRGetPropertyString,
    SVF_Emphasis, DISPID_SVGetAudioInputs
)


class SpeechStreamSeekPositionType(IntFlag):
    SSSPTRelativeToStart = 0
    SSSPTRelativeToCurrentPosition = 1
    SSSPTRelativeToEnd = 2


class SPFILEMODE(IntFlag):
    SPFM_OPEN_READONLY = 0
    SPFM_OPEN_READWRITE = 1
    SPFM_CREATE = 2
    SPFM_CREATE_ALWAYS = 3
    SPFM_NUM_MODES = 4


class SPVISEMES(IntFlag):
    SP_VISEME_0 = 0
    SP_VISEME_1 = 1
    SP_VISEME_2 = 2
    SP_VISEME_3 = 3
    SP_VISEME_4 = 4
    SP_VISEME_5 = 5
    SP_VISEME_6 = 6
    SP_VISEME_7 = 7
    SP_VISEME_8 = 8
    SP_VISEME_9 = 9
    SP_VISEME_10 = 10
    SP_VISEME_11 = 11
    SP_VISEME_12 = 12
    SP_VISEME_13 = 13
    SP_VISEME_14 = 14
    SP_VISEME_15 = 15
    SP_VISEME_16 = 16
    SP_VISEME_17 = 17
    SP_VISEME_18 = 18
    SP_VISEME_19 = 19
    SP_VISEME_20 = 20
    SP_VISEME_21 = 21


class SPVPRIORITY(IntFlag):
    SPVPRI_NORMAL = 0
    SPVPRI_ALERT = 1
    SPVPRI_OVER = 2


class SPEVENTENUM(IntFlag):
    SPEI_UNDEFINED = 0
    SPEI_START_INPUT_STREAM = 1
    SPEI_END_INPUT_STREAM = 2
    SPEI_VOICE_CHANGE = 3
    SPEI_TTS_BOOKMARK = 4
    SPEI_WORD_BOUNDARY = 5
    SPEI_PHONEME = 6
    SPEI_SENTENCE_BOUNDARY = 7
    SPEI_VISEME = 8
    SPEI_TTS_AUDIO_LEVEL = 9
    SPEI_TTS_PRIVATE = 15
    SPEI_MIN_TTS = 1
    SPEI_MAX_TTS = 15
    SPEI_END_SR_STREAM = 34
    SPEI_SOUND_START = 35
    SPEI_SOUND_END = 36
    SPEI_PHRASE_START = 37
    SPEI_RECOGNITION = 38
    SPEI_HYPOTHESIS = 39
    SPEI_SR_BOOKMARK = 40
    SPEI_PROPERTY_NUM_CHANGE = 41
    SPEI_PROPERTY_STRING_CHANGE = 42
    SPEI_FALSE_RECOGNITION = 43
    SPEI_INTERFERENCE = 44
    SPEI_REQUEST_UI = 45
    SPEI_RECO_STATE_CHANGE = 46
    SPEI_ADAPTATION = 47
    SPEI_START_SR_STREAM = 48
    SPEI_RECO_OTHER_CONTEXT = 49
    SPEI_SR_AUDIO_LEVEL = 50
    SPEI_SR_RETAINEDAUDIO = 51
    SPEI_SR_PRIVATE = 52
    SPEI_ACTIVE_CATEGORY_CHANGED = 53
    SPEI_RESERVED5 = 54
    SPEI_RESERVED6 = 55
    SPEI_MIN_SR = 34
    SPEI_MAX_SR = 55
    SPEI_RESERVED1 = 30
    SPEI_RESERVED2 = 33
    SPEI_RESERVED3 = 63


class SpeechVoiceSpeakFlags(IntFlag):
    SVSFDefault = 0
    SVSFlagsAsync = 1
    SVSFPurgeBeforeSpeak = 2
    SVSFIsFilename = 4
    SVSFIsXML = 8
    SVSFIsNotXML = 16
    SVSFPersistXML = 32
    SVSFNLPSpeakPunc = 64
    SVSFParseSapi = 128
    SVSFParseSsml = 256
    SVSFParseAutodetect = 0
    SVSFNLPMask = 64
    SVSFParseMask = 384
    SVSFVoiceMask = 511
    SVSFUnusedFlags = -512


class SpeechAudioState(IntFlag):
    SASClosed = 0
    SASStop = 1
    SASPause = 2
    SASRun = 3


class SPRECOSTATE(IntFlag):
    SPRST_INACTIVE = 0
    SPRST_ACTIVE = 1
    SPRST_ACTIVE_ALWAYS = 2
    SPRST_INACTIVE_WITH_PURGE = 3
    SPRST_NUM_STATES = 4


class _SPAUDIOSTATE(IntFlag):
    SPAS_CLOSED = 0
    SPAS_STOP = 1
    SPAS_PAUSE = 2
    SPAS_RUN = 3


class SpeechAudioFormatType(IntFlag):
    SAFTDefault = -1
    SAFTNoAssignedFormat = 0
    SAFTText = 1
    SAFTNonStandardFormat = 2
    SAFTExtendedAudioFormat = 3
    SAFT8kHz8BitMono = 4
    SAFT8kHz8BitStereo = 5
    SAFT8kHz16BitMono = 6
    SAFT8kHz16BitStereo = 7
    SAFT11kHz8BitMono = 8
    SAFT11kHz8BitStereo = 9
    SAFT11kHz16BitMono = 10
    SAFT11kHz16BitStereo = 11
    SAFT12kHz8BitMono = 12
    SAFT12kHz8BitStereo = 13
    SAFT12kHz16BitMono = 14
    SAFT12kHz16BitStereo = 15
    SAFT16kHz8BitMono = 16
    SAFT16kHz8BitStereo = 17
    SAFT16kHz16BitMono = 18
    SAFT16kHz16BitStereo = 19
    SAFT22kHz8BitMono = 20
    SAFT22kHz8BitStereo = 21
    SAFT22kHz16BitMono = 22
    SAFT22kHz16BitStereo = 23
    SAFT24kHz8BitMono = 24
    SAFT24kHz8BitStereo = 25
    SAFT24kHz16BitMono = 26
    SAFT24kHz16BitStereo = 27
    SAFT32kHz8BitMono = 28
    SAFT32kHz8BitStereo = 29
    SAFT32kHz16BitMono = 30
    SAFT32kHz16BitStereo = 31
    SAFT44kHz8BitMono = 32
    SAFT44kHz8BitStereo = 33
    SAFT44kHz16BitMono = 34
    SAFT44kHz16BitStereo = 35
    SAFT48kHz8BitMono = 36
    SAFT48kHz8BitStereo = 37
    SAFT48kHz16BitMono = 38
    SAFT48kHz16BitStereo = 39
    SAFTTrueSpeech_8kHz1BitMono = 40
    SAFTCCITT_ALaw_8kHzMono = 41
    SAFTCCITT_ALaw_8kHzStereo = 42
    SAFTCCITT_ALaw_11kHzMono = 43
    SAFTCCITT_ALaw_11kHzStereo = 44
    SAFTCCITT_ALaw_22kHzMono = 45
    SAFTCCITT_ALaw_22kHzStereo = 46
    SAFTCCITT_ALaw_44kHzMono = 47
    SAFTCCITT_ALaw_44kHzStereo = 48
    SAFTCCITT_uLaw_8kHzMono = 49
    SAFTCCITT_uLaw_8kHzStereo = 50
    SAFTCCITT_uLaw_11kHzMono = 51
    SAFTCCITT_uLaw_11kHzStereo = 52
    SAFTCCITT_uLaw_22kHzMono = 53
    SAFTCCITT_uLaw_22kHzStereo = 54
    SAFTCCITT_uLaw_44kHzMono = 55
    SAFTCCITT_uLaw_44kHzStereo = 56
    SAFTADPCM_8kHzMono = 57
    SAFTADPCM_8kHzStereo = 58
    SAFTADPCM_11kHzMono = 59
    SAFTADPCM_11kHzStereo = 60
    SAFTADPCM_22kHzMono = 61
    SAFTADPCM_22kHzStereo = 62
    SAFTADPCM_44kHzMono = 63
    SAFTADPCM_44kHzStereo = 64
    SAFTGSM610_8kHzMono = 65
    SAFTGSM610_11kHzMono = 66
    SAFTGSM610_22kHzMono = 67
    SAFTGSM610_44kHzMono = 68


class SpeechVisemeFeature(IntFlag):
    SVF_None = 0
    SVF_Stressed = 1
    SVF_Emphasis = 2


class SPWAVEFORMATTYPE(IntFlag):
    SPWF_INPUT = 0
    SPWF_SRENGINE = 1


class SpeechVoiceEvents(IntFlag):
    SVEStartInputStream = 2
    SVEEndInputStream = 4
    SVEVoiceChange = 8
    SVEBookmark = 16
    SVEWordBoundary = 32
    SVEPhoneme = 64
    SVESentenceBoundary = 128
    SVEViseme = 256
    SVEAudioLevel = 512
    SVEPrivate = 32768
    SVEAllEvents = 33790


class SpeechVoicePriority(IntFlag):
    SVPNormal = 0
    SVPAlert = 1
    SVPOver = 2


class SPAUDIOOPTIONS(IntFlag):
    SPAO_NONE = 0
    SPAO_RETAIN_AUDIO = 1


class SPBOOKMARKOPTIONS(IntFlag):
    SPBO_NONE = 0
    SPBO_PAUSE = 1
    SPBO_AHEAD = 2
    SPBO_TIME_UNITS = 4


class SPCONTEXTSTATE(IntFlag):
    SPCS_DISABLED = 0
    SPCS_ENABLED = 1


class SpeechInterference(IntFlag):
    SINone = 0
    SINoise = 1
    SINoSignal = 2
    SITooLoud = 3
    SITooQuiet = 4
    SITooFast = 5
    SITooSlow = 6


class SpeechStreamFileMode(IntFlag):
    SSFMOpenForRead = 0
    SSFMOpenReadWrite = 1
    SSFMCreate = 2
    SSFMCreateForWrite = 3


class SpeechRunState(IntFlag):
    SRSEDone = 1
    SRSEIsSpeaking = 2


class SPSEMANTICFORMAT(IntFlag):
    SPSMF_SAPI_PROPERTIES = 0
    SPSMF_SRGS_SEMANTICINTERPRETATION_MS = 1
    SPSMF_SRGS_SAPIPROPERTIES = 2
    SPSMF_UPS = 4
    SPSMF_SRGS_SEMANTICINTERPRETATION_W3C = 8


class SPGRAMMARWORDTYPE(IntFlag):
    SPWT_DISPLAY = 0
    SPWT_LEXICAL = 1
    SPWT_PRONUNCIATION = 2
    SPWT_LEXICAL_NO_SPECIAL_CHARS = 3


class SPLOADOPTIONS(IntFlag):
    SPLO_STATIC = 0
    SPLO_DYNAMIC = 1


class SPRULESTATE(IntFlag):
    SPRS_INACTIVE = 0
    SPRS_ACTIVE = 1
    SPRS_ACTIVE_WITH_AUTO_PAUSE = 3
    SPRS_ACTIVE_USER_DELIMITED = 4


class SPWORDPRONOUNCEABLE(IntFlag):
    SPWP_UNKNOWN_WORD_UNPRONOUNCEABLE = 0
    SPWP_UNKNOWN_WORD_PRONOUNCEABLE = 1
    SPWP_KNOWN_WORD_PRONOUNCEABLE = 2


class SPGRAMMARSTATE(IntFlag):
    SPGS_DISABLED = 0
    SPGS_ENABLED = 1
    SPGS_EXCLUSIVE = 3


class SpeechTokenContext(IntFlag):
    STCInprocServer = 1
    STCInprocHandler = 2
    STCLocalServer = 4
    STCRemoteServer = 16
    STCAll = 23


class SpeechTokenShellFolder(IntFlag):
    STSF_AppData = 26
    STSF_LocalAppData = 28
    STSF_CommonAppData = 35
    STSF_FlagCreate = 32768


class SpeechDataKeyLocation(IntFlag):
    SDKLDefaultLocation = 0
    SDKLCurrentUser = 1
    SDKLLocalMachine = 2
    SDKLCurrentConfig = 5


class SpeechRecoEvents(IntFlag):
    SREStreamEnd = 1
    SRESoundStart = 2
    SRESoundEnd = 4
    SREPhraseStart = 8
    SRERecognition = 16
    SREHypothesis = 32
    SREBookmark = 64
    SREPropertyNumChange = 128
    SREPropertyStringChange = 256
    SREFalseRecognition = 512
    SREInterference = 1024
    SRERequestUI = 2048
    SREStateChange = 4096
    SREAdaptation = 8192
    SREStreamStart = 16384
    SRERecoOtherContext = 32768
    SREAudioLevel = 65536
    SREPrivate = 262144
    SREAllEvents = 393215


class SpeechRecoContextState(IntFlag):
    SRCS_Disabled = 0
    SRCS_Enabled = 1


class SpeechRetainedAudioOptions(IntFlag):
    SRAONone = 0
    SRAORetainAudio = 1


class SpeechBookmarkOptions(IntFlag):
    SBONone = 0
    SBOPause = 1


class SPINTERFERENCE(IntFlag):
    SPINTERFERENCE_NONE = 0
    SPINTERFERENCE_NOISE = 1
    SPINTERFERENCE_NOSIGNAL = 2
    SPINTERFERENCE_TOOLOUD = 3
    SPINTERFERENCE_TOOQUIET = 4
    SPINTERFERENCE_TOOFAST = 5
    SPINTERFERENCE_TOOSLOW = 6
    SPINTERFERENCE_LATENCY_WARNING = 7
    SPINTERFERENCE_LATENCY_TRUNCATE_BEGIN = 8
    SPINTERFERENCE_LATENCY_TRUNCATE_END = 9


class SpeechGrammarState(IntFlag):
    SGSEnabled = 1
    SGSDisabled = 0
    SGSExclusive = 3


class SPADAPTATIONRELEVANCE(IntFlag):
    SPAR_Unknown = 0
    SPAR_Low = 1
    SPAR_Medium = 2
    SPAR_High = 3


class SPCATEGORYTYPE(IntFlag):
    SPCT_COMMAND = 0
    SPCT_DICTATION = 1
    SPCT_SLEEP = 2
    SPCT_SUB_COMMAND = 3
    SPCT_SUB_DICTATION = 4


class SpeechRecognitionType(IntFlag):
    SRTStandard = 0
    SRTAutopause = 1
    SRTEmulated = 2
    SRTSMLTimeout = 4
    SRTExtendableParse = 8
    SRTReSent = 16


class SpeechRecognizerState(IntFlag):
    SRSInactive = 0
    SRSActive = 1
    SRSActiveAlways = 2
    SRSInactiveWithPurge = 3


class SPPARTOFSPEECH(IntFlag):
    SPPS_NotOverriden = -1
    SPPS_Unknown = 0
    SPPS_Noun = 4096
    SPPS_Verb = 8192
    SPPS_Modifier = 12288
    SPPS_Function = 16384
    SPPS_Interjection = 20480
    SPPS_Noncontent = 24576
    SPPS_LMA = 28672
    SPPS_SuppressWord = 61440


class SpeechLoadOption(IntFlag):
    SLOStatic = 0
    SLODynamic = 1


class SPLEXICONTYPE(IntFlag):
    eLEXTYPE_USER = 1
    eLEXTYPE_APP = 2
    eLEXTYPE_VENDORLEXICON = 4
    eLEXTYPE_LETTERTOSOUND = 8
    eLEXTYPE_MORPHOLOGY = 16
    eLEXTYPE_RESERVED4 = 32
    eLEXTYPE_USER_SHORTCUT = 64
    eLEXTYPE_RESERVED6 = 128
    eLEXTYPE_RESERVED7 = 256
    eLEXTYPE_RESERVED8 = 512
    eLEXTYPE_RESERVED9 = 1024
    eLEXTYPE_RESERVED10 = 2048
    eLEXTYPE_PRIVATE1 = 4096
    eLEXTYPE_PRIVATE2 = 8192
    eLEXTYPE_PRIVATE3 = 16384
    eLEXTYPE_PRIVATE4 = 32768
    eLEXTYPE_PRIVATE5 = 65536
    eLEXTYPE_PRIVATE6 = 131072
    eLEXTYPE_PRIVATE7 = 262144
    eLEXTYPE_PRIVATE8 = 524288
    eLEXTYPE_PRIVATE9 = 1048576
    eLEXTYPE_PRIVATE10 = 2097152
    eLEXTYPE_PRIVATE11 = 4194304
    eLEXTYPE_PRIVATE12 = 8388608
    eLEXTYPE_PRIVATE13 = 16777216
    eLEXTYPE_PRIVATE14 = 33554432
    eLEXTYPE_PRIVATE15 = 67108864
    eLEXTYPE_PRIVATE16 = 134217728
    eLEXTYPE_PRIVATE17 = 268435456
    eLEXTYPE_PRIVATE18 = 536870912
    eLEXTYPE_PRIVATE19 = 1073741824
    eLEXTYPE_PRIVATE20 = -2147483648


class SpeechRuleState(IntFlag):
    SGDSInactive = 0
    SGDSActive = 1
    SGDSActiveWithAutoPause = 3
    SGDSActiveUserDelimited = 4


class SPWORDTYPE(IntFlag):
    eWORDTYPE_ADDED = 1
    eWORDTYPE_DELETED = 2


class SpeechWordPronounceable(IntFlag):
    SWPUnknownWordUnpronounceable = 0
    SWPUnknownWordPronounceable = 1
    SWPKnownWordPronounceable = 2


class SPSHORTCUTTYPE(IntFlag):
    SPSHT_NotOverriden = -1
    SPSHT_Unknown = 0
    SPSHT_EMAIL = 4096
    SPSHT_OTHER = 8192
    SPPS_RESERVED1 = 12288
    SPPS_RESERVED2 = 16384
    SPPS_RESERVED3 = 20480
    SPPS_RESERVED4 = 61440


class SpeechEngineConfidence(IntFlag):
    SECLowConfidence = -1
    SECNormalConfidence = 0
    SECHighConfidence = 1


class SpeechVisemeType(IntFlag):
    SVP_0 = 0
    SVP_1 = 1
    SVP_2 = 2
    SVP_3 = 3
    SVP_4 = 4
    SVP_5 = 5
    SVP_6 = 6
    SVP_7 = 7
    SVP_8 = 8
    SVP_9 = 9
    SVP_10 = 10
    SVP_11 = 11
    SVP_12 = 12
    SVP_13 = 13
    SVP_14 = 14
    SVP_15 = 15
    SVP_16 = 16
    SVP_17 = 17
    SVP_18 = 18
    SVP_19 = 19
    SVP_20 = 20
    SVP_21 = 21


class SpeechDisplayAttributes(IntFlag):
    SDA_No_Trailing_Space = 0
    SDA_One_Trailing_Space = 2
    SDA_Two_Trailing_Spaces = 4
    SDA_Consume_Leading_Spaces = 8


class SpeechDiscardType(IntFlag):
    SDTProperty = 1
    SDTReplacement = 2
    SDTRule = 4
    SDTDisplayText = 8
    SDTLexicalForm = 16
    SDTPronunciation = 32
    SDTAudio = 64
    SDTAlternates = 128
    SDTAll = 255


class SpeechFormatType(IntFlag):
    SFTInput = 0
    SFTSREngine = 1


class SpeechRuleAttributes(IntFlag):
    SRATopLevel = 1
    SRADefaultToActive = 2
    SRAExport = 4
    SRAImport = 8
    SRAInterpreter = 16
    SRADynamic = 32
    SRARoot = 64


class SpeechLexiconType(IntFlag):
    SLTUser = 1
    SLTApp = 2


class SPXMLRESULTOPTIONS(IntFlag):
    SPXRO_SML = 0
    SPXRO_Alternates_SML = 1


class SpeechWordType(IntFlag):
    SWTAdded = 1
    SWTDeleted = 2


class SpeechPartOfSpeech(IntFlag):
    SPSNotOverriden = -1
    SPSUnknown = 0
    SPSNoun = 4096
    SPSVerb = 8192
    SPSModifier = 12288
    SPSFunction = 16384
    SPSInterjection = 20480
    SPSLMA = 28672
    SPSSuppressWord = 61440


class DISPID_SpeechDataKey(IntFlag):
    DISPID_SDKSetBinaryValue = 1
    DISPID_SDKGetBinaryValue = 2
    DISPID_SDKSetStringValue = 3
    DISPID_SDKGetStringValue = 4
    DISPID_SDKSetLongValue = 5
    DISPID_SDKGetlongValue = 6
    DISPID_SDKOpenKey = 7
    DISPID_SDKCreateKey = 8
    DISPID_SDKDeleteKey = 9
    DISPID_SDKDeleteValue = 10
    DISPID_SDKEnumKeys = 11
    DISPID_SDKEnumValues = 12


class DISPID_SpeechObjectToken(IntFlag):
    DISPID_SOTId = 1
    DISPID_SOTDataKey = 2
    DISPID_SOTCategory = 3
    DISPID_SOTGetDescription = 4
    DISPID_SOTSetId = 5
    DISPID_SOTGetAttribute = 6
    DISPID_SOTCreateInstance = 7
    DISPID_SOTRemove = 8
    DISPID_SOTGetStorageFileName = 9
    DISPID_SOTRemoveStorageFileName = 10
    DISPID_SOTIsUISupported = 11
    DISPID_SOTDisplayUI = 12
    DISPID_SOTMatchesAttributes = 13


class DISPID_SpeechObjectTokens(IntFlag):
    DISPID_SOTsCount = 1
    DISPID_SOTsItem = 0
    DISPID_SOTs_NewEnum = -4


class DISPID_SpeechObjectTokenCategory(IntFlag):
    DISPID_SOTCId = 1
    DISPID_SOTCDefault = 2
    DISPID_SOTCSetId = 3
    DISPID_SOTCGetDataKey = 4
    DISPID_SOTCEnumerateTokens = 5


class DISPID_SpeechAudioFormat(IntFlag):
    DISPID_SAFType = 1
    DISPID_SAFGuid = 2
    DISPID_SAFGetWaveFormatEx = 3
    DISPID_SAFSetWaveFormatEx = 4


class DISPID_SpeechBaseStream(IntFlag):
    DISPID_SBSFormat = 1
    DISPID_SBSRead = 2
    DISPID_SBSWrite = 3
    DISPID_SBSSeek = 4


class SpeechGrammarWordType(IntFlag):
    SGDisplay = 0
    SGLexical = 1
    SGPronounciation = 2
    SGLexicalNoSpecialChars = 3


class SpeechSpecialTransitionType(IntFlag):
    SSTTWildcard = 1
    SSTTDictation = 2
    SSTTTextBuffer = 3


class DISPID_SpeechAudio(IntFlag):
    DISPID_SAStatus = 200
    DISPID_SABufferInfo = 201
    DISPID_SADefaultFormat = 202
    DISPID_SAVolume = 203
    DISPID_SABufferNotifySize = 204
    DISPID_SAEventHandle = 205
    DISPID_SASetState = 206


class DISPID_SpeechMMSysAudio(IntFlag):
    DISPID_SMSADeviceId = 300
    DISPID_SMSALineId = 301
    DISPID_SMSAMMHandle = 302


class DISPID_SpeechFileStream(IntFlag):
    DISPID_SFSOpen = 100
    DISPID_SFSClose = 101


class SpeechGrammarRuleStateTransitionType(IntFlag):
    SGRSTTEpsilon = 0
    SGRSTTWord = 1
    SGRSTTRule = 2
    SGRSTTDictation = 3
    SGRSTTWildcard = 4
    SGRSTTTextBuffer = 5


class DISPID_SpeechCustomStream(IntFlag):
    DISPID_SCSBaseStream = 100


class DISPID_SpeechMemoryStream(IntFlag):
    DISPID_SMSSetData = 100
    DISPID_SMSGetData = 101


class DISPID_SpeechAudioStatus(IntFlag):
    DISPID_SASFreeBufferSpace = 1
    DISPID_SASNonBlockingIO = 2
    DISPID_SASState = 3
    DISPID_SASCurrentSeekPosition = 4
    DISPID_SASCurrentDevicePosition = 5


class DISPID_SpeechAudioBufferInfo(IntFlag):
    DISPID_SABIMinNotification = 1
    DISPID_SABIBufferSize = 2
    DISPID_SABIEventBias = 3


class DISPID_SpeechWaveFormatEx(IntFlag):
    DISPID_SWFEFormatTag = 1
    DISPID_SWFEChannels = 2
    DISPID_SWFESamplesPerSec = 3
    DISPID_SWFEAvgBytesPerSec = 4
    DISPID_SWFEBlockAlign = 5
    DISPID_SWFEBitsPerSample = 6
    DISPID_SWFEExtraData = 7


class DISPID_SpeechVoice(IntFlag):
    DISPID_SVStatus = 1
    DISPID_SVVoice = 2
    DISPID_SVAudioOutput = 3
    DISPID_SVAudioOutputStream = 4
    DISPID_SVRate = 5
    DISPID_SVVolume = 6
    DISPID_SVAllowAudioOuputFormatChangesOnNextSet = 7
    DISPID_SVEventInterests = 8
    DISPID_SVPriority = 9
    DISPID_SVAlertBoundary = 10
    DISPID_SVSyncronousSpeakTimeout = 11
    DISPID_SVSpeak = 12
    DISPID_SVSpeakStream = 13
    DISPID_SVPause = 14
    DISPID_SVResume = 15
    DISPID_SVSkip = 16
    DISPID_SVGetVoices = 17
    DISPID_SVGetAudioOutputs = 18
    DISPID_SVWaitUntilDone = 19
    DISPID_SVSpeakCompleteEvent = 20
    DISPID_SVIsUISupported = 21
    DISPID_SVDisplayUI = 22


class DISPID_SpeechVoiceStatus(IntFlag):
    DISPID_SVSCurrentStreamNumber = 1
    DISPID_SVSLastStreamNumberQueued = 2
    DISPID_SVSLastResult = 3
    DISPID_SVSRunningState = 4
    DISPID_SVSInputWordPosition = 5
    DISPID_SVSInputWordLength = 6
    DISPID_SVSInputSentencePosition = 7
    DISPID_SVSInputSentenceLength = 8
    DISPID_SVSLastBookmark = 9
    DISPID_SVSLastBookmarkId = 10
    DISPID_SVSPhonemeId = 11
    DISPID_SVSVisemeId = 12


class DISPID_SpeechVoiceEvent(IntFlag):
    DISPID_SVEStreamStart = 1
    DISPID_SVEStreamEnd = 2
    DISPID_SVEVoiceChange = 3
    DISPID_SVEBookmark = 4
    DISPID_SVEWord = 5
    DISPID_SVEPhoneme = 6
    DISPID_SVESentenceBoundary = 7
    DISPID_SVEViseme = 8
    DISPID_SVEAudioLevel = 9
    DISPID_SVEEnginePrivate = 10


class DISPID_SpeechRecognizer(IntFlag):
    DISPID_SRRecognizer = 1
    DISPID_SRAllowAudioInputFormatChangesOnNextSet = 2
    DISPID_SRAudioInput = 3
    DISPID_SRAudioInputStream = 4
    DISPID_SRIsShared = 5
    DISPID_SRState = 6
    DISPID_SRStatus = 7
    DISPID_SRProfile = 8
    DISPID_SREmulateRecognition = 9
    DISPID_SRCreateRecoContext = 10
    DISPID_SRGetFormat = 11
    DISPID_SRSetPropertyNumber = 12
    DISPID_SRGetPropertyNumber = 13
    DISPID_SRSetPropertyString = 14
    DISPID_SRGetPropertyString = 15
    DISPID_SRIsUISupported = 16
    DISPID_SRDisplayUI = 17
    DISPID_SRGetRecognizers = 18
    DISPID_SVGetAudioInputs = 19
    DISPID_SVGetProfiles = 20


class SpeechEmulationCompareFlags(IntFlag):
    SECFIgnoreCase = 1
    SECFIgnoreKanaType = 65536
    SECFIgnoreWidth = 131072
    SECFNoSpecialChars = 536870912
    SECFEmulateResult = 1073741824
    SECFDefault = 196609


class DISPID_SpeechRecognizerStatus(IntFlag):
    DISPID_SRSAudioStatus = 1
    DISPID_SRSCurrentStreamPosition = 2
    DISPID_SRSCurrentStreamNumber = 3
    DISPID_SRSNumberOfActiveRules = 4
    DISPID_SRSClsidEngine = 5
    DISPID_SRSSupportedLanguages = 6


class DISPID_SpeechRecoContext(IntFlag):
    DISPID_SRCRecognizer = 1
    DISPID_SRCAudioInInterferenceStatus = 2
    DISPID_SRCRequestedUIType = 3
    DISPID_SRCVoice = 4
    DISPID_SRAllowVoiceFormatMatchingOnNextSet = 5
    DISPID_SRCVoicePurgeEvent = 6
    DISPID_SRCEventInterests = 7
    DISPID_SRCCmdMaxAlternates = 8
    DISPID_SRCState = 9
    DISPID_SRCRetainedAudio = 10
    DISPID_SRCRetainedAudioFormat = 11
    DISPID_SRCPause = 12
    DISPID_SRCResume = 13
    DISPID_SRCCreateGrammar = 14
    DISPID_SRCCreateResultFromMemory = 15
    DISPID_SRCBookmark = 16
    DISPID_SRCSetAdaptationData = 17


class DISPIDSPRG(IntFlag):
    DISPID_SRGId = 1
    DISPID_SRGRecoContext = 2
    DISPID_SRGState = 3
    DISPID_SRGRules = 4
    DISPID_SRGReset = 5
    DISPID_SRGCommit = 6
    DISPID_SRGCmdLoadFromFile = 7
    DISPID_SRGCmdLoadFromObject = 8
    DISPID_SRGCmdLoadFromResource = 9
    DISPID_SRGCmdLoadFromMemory = 10
    DISPID_SRGCmdLoadFromProprietaryGrammar = 11
    DISPID_SRGCmdSetRuleState = 12
    DISPID_SRGCmdSetRuleIdState = 13
    DISPID_SRGDictationLoad = 14
    DISPID_SRGDictationUnload = 15
    DISPID_SRGDictationSetState = 16
    DISPID_SRGSetWordSequenceData = 17
    DISPID_SRGSetTextSelection = 18
    DISPID_SRGIsPronounceable = 19


class DISPID_SpeechRecoContextEvents(IntFlag):
    DISPID_SRCEStartStream = 1
    DISPID_SRCEEndStream = 2
    DISPID_SRCEBookmark = 3
    DISPID_SRCESoundStart = 4
    DISPID_SRCESoundEnd = 5
    DISPID_SRCEPhraseStart = 6
    DISPID_SRCERecognition = 7
    DISPID_SRCEHypothesis = 8
    DISPID_SRCEPropertyNumberChange = 9
    DISPID_SRCEPropertyStringChange = 10
    DISPID_SRCEFalseRecognition = 11
    DISPID_SRCEInterference = 12
    DISPID_SRCERequestUI = 13
    DISPID_SRCERecognizerStateChange = 14
    DISPID_SRCEAdaptation = 15
    DISPID_SRCERecognitionForOtherContext = 16
    DISPID_SRCEAudioLevel = 17
    DISPID_SRCEEnginePrivate = 18


class DISPID_SpeechGrammarRule(IntFlag):
    DISPID_SGRAttributes = 1
    DISPID_SGRInitialState = 2
    DISPID_SGRName = 3
    DISPID_SGRId = 4
    DISPID_SGRClear = 5
    DISPID_SGRAddResource = 6
    DISPID_SGRAddState = 7


class DISPID_SpeechGrammarRules(IntFlag):
    DISPID_SGRsCount = 1
    DISPID_SGRsDynamic = 2
    DISPID_SGRsAdd = 3
    DISPID_SGRsCommit = 4
    DISPID_SGRsCommitAndSave = 5
    DISPID_SGRsFindRule = 6
    DISPID_SGRsItem = 0
    DISPID_SGRs_NewEnum = -4


class DISPID_SpeechGrammarRuleState(IntFlag):
    DISPID_SGRSRule = 1
    DISPID_SGRSTransitions = 2
    DISPID_SGRSAddWordTransition = 3
    DISPID_SGRSAddRuleTransition = 4
    DISPID_SGRSAddSpecialTransition = 5


class DISPID_SpeechGrammarRuleStateTransitions(IntFlag):
    DISPID_SGRSTsCount = 1
    DISPID_SGRSTsItem = 0
    DISPID_SGRSTs_NewEnum = -4


class DISPID_SpeechGrammarRuleStateTransition(IntFlag):
    DISPID_SGRSTType = 1
    DISPID_SGRSTText = 2
    DISPID_SGRSTRule = 3
    DISPID_SGRSTWeight = 4
    DISPID_SGRSTPropertyName = 5
    DISPID_SGRSTPropertyId = 6
    DISPID_SGRSTPropertyValue = 7
    DISPID_SGRSTNextState = 8


class DISPIDSPTSI(IntFlag):
    DISPIDSPTSI_ActiveOffset = 1
    DISPIDSPTSI_ActiveLength = 2
    DISPIDSPTSI_SelectionOffset = 3
    DISPIDSPTSI_SelectionLength = 4


class DISPID_SpeechRecoResult(IntFlag):
    DISPID_SRRRecoContext = 1
    DISPID_SRRTimes = 2
    DISPID_SRRAudioFormat = 3
    DISPID_SRRPhraseInfo = 4
    DISPID_SRRAlternates = 5
    DISPID_SRRAudio = 6
    DISPID_SRRSpeakAudio = 7
    DISPID_SRRSaveToMemory = 8
    DISPID_SRRDiscardResultInfo = 9


class DISPID_SpeechXMLRecoResult(IntFlag):
    DISPID_SRRGetXMLResult = 10
    DISPID_SRRGetXMLErrorInfo = 11


class DISPID_SpeechRecoResult2(IntFlag):
    DISPID_SRRSetTextFeedback = 12


class DISPID_SpeechPhraseBuilder(IntFlag):
    DISPID_SPPBRestorePhraseFromMemory = 1


class DISPID_SpeechRecoResultTimes(IntFlag):
    DISPID_SRRTStreamTime = 1
    DISPID_SRRTLength = 2
    DISPID_SRRTTickCount = 3
    DISPID_SRRTOffsetFromStart = 4


class DISPID_SpeechPhraseAlternate(IntFlag):
    DISPID_SPARecoResult = 1
    DISPID_SPAStartElementInResult = 2
    DISPID_SPANumberOfElementsInResult = 3
    DISPID_SPAPhraseInfo = 4
    DISPID_SPACommit = 5


class DISPID_SpeechPhraseAlternates(IntFlag):
    DISPID_SPAsCount = 1
    DISPID_SPAsItem = 0
    DISPID_SPAs_NewEnum = -4


class DISPID_SpeechPhraseInfo(IntFlag):
    DISPID_SPILanguageId = 1
    DISPID_SPIGrammarId = 2
    DISPID_SPIStartTime = 3
    DISPID_SPIAudioStreamPosition = 4
    DISPID_SPIAudioSizeBytes = 5
    DISPID_SPIRetainedSizeBytes = 6
    DISPID_SPIAudioSizeTime = 7
    DISPID_SPIRule = 8
    DISPID_SPIProperties = 9
    DISPID_SPIElements = 10
    DISPID_SPIReplacements = 11
    DISPID_SPIEngineId = 12
    DISPID_SPIEnginePrivateData = 13
    DISPID_SPISaveToMemory = 14
    DISPID_SPIGetText = 15
    DISPID_SPIGetDisplayAttributes = 16


class DISPID_SpeechPhraseElement(IntFlag):
    DISPID_SPEAudioTimeOffset = 1
    DISPID_SPEAudioSizeTime = 2
    DISPID_SPEAudioStreamOffset = 3
    DISPID_SPEAudioSizeBytes = 4
    DISPID_SPERetainedStreamOffset = 5
    DISPID_SPERetainedSizeBytes = 6
    DISPID_SPEDisplayText = 7
    DISPID_SPELexicalForm = 8
    DISPID_SPEPronunciation = 9
    DISPID_SPEDisplayAttributes = 10
    DISPID_SPERequiredConfidence = 11
    DISPID_SPEActualConfidence = 12
    DISPID_SPEEngineConfidence = 13


class DISPID_SpeechPhraseElements(IntFlag):
    DISPID_SPEsCount = 1
    DISPID_SPEsItem = 0
    DISPID_SPEs_NewEnum = -4


class DISPID_SpeechPhraseReplacement(IntFlag):
    DISPID_SPRDisplayAttributes = 1
    DISPID_SPRText = 2
    DISPID_SPRFirstElement = 3
    DISPID_SPRNumberOfElements = 4


class DISPID_SpeechPhraseReplacements(IntFlag):
    DISPID_SPRsCount = 1
    DISPID_SPRsItem = 0
    DISPID_SPRs_NewEnum = -4


class DISPID_SpeechPhraseProperty(IntFlag):
    DISPID_SPPName = 1
    DISPID_SPPId = 2
    DISPID_SPPValue = 3
    DISPID_SPPFirstElement = 4
    DISPID_SPPNumberOfElements = 5
    DISPID_SPPEngineConfidence = 6
    DISPID_SPPConfidence = 7
    DISPID_SPPParent = 8
    DISPID_SPPChildren = 9


class DISPID_SpeechPhraseProperties(IntFlag):
    DISPID_SPPsCount = 1
    DISPID_SPPsItem = 0
    DISPID_SPPs_NewEnum = -4


class DISPID_SpeechPhraseRule(IntFlag):
    DISPID_SPRuleName = 1
    DISPID_SPRuleId = 2
    DISPID_SPRuleFirstElement = 3
    DISPID_SPRuleNumberOfElements = 4
    DISPID_SPRuleParent = 5
    DISPID_SPRuleChildren = 6
    DISPID_SPRuleConfidence = 7
    DISPID_SPRuleEngineConfidence = 8


class DISPID_SpeechPhraseRules(IntFlag):
    DISPID_SPRulesCount = 1
    DISPID_SPRulesItem = 0
    DISPID_SPRules_NewEnum = -4


class DISPID_SpeechLexicon(IntFlag):
    DISPID_SLGenerationId = 1
    DISPID_SLGetWords = 2
    DISPID_SLAddPronunciation = 3
    DISPID_SLAddPronunciationByPhoneIds = 4
    DISPID_SLRemovePronunciation = 5
    DISPID_SLRemovePronunciationByPhoneIds = 6
    DISPID_SLGetPronunciations = 7
    DISPID_SLGetGenerationChange = 8


class DISPID_SpeechLexiconWords(IntFlag):
    DISPID_SLWsCount = 1
    DISPID_SLWsItem = 0
    DISPID_SLWs_NewEnum = -4


class DISPID_SpeechLexiconWord(IntFlag):
    DISPID_SLWLangId = 1
    DISPID_SLWType = 2
    DISPID_SLWWord = 3
    DISPID_SLWPronunciations = 4


class DISPID_SpeechLexiconProns(IntFlag):
    DISPID_SLPsCount = 1
    DISPID_SLPsItem = 0
    DISPID_SLPs_NewEnum = -4


class DISPID_SpeechLexiconPronunciation(IntFlag):
    DISPID_SLPType = 1
    DISPID_SLPLangId = 2
    DISPID_SLPPartOfSpeech = 3
    DISPID_SLPPhoneIds = 4
    DISPID_SLPSymbolic = 5


class DISPID_SpeechPhoneConverter(IntFlag):
    DISPID_SPCLangId = 1
    DISPID_SPCPhoneToId = 2
    DISPID_SPCIdToPhone = 3


class SPDATAKEYLOCATION(IntFlag):
    SPDKL_DefaultLocation = 0
    SPDKL_CurrentUser = 1
    SPDKL_LocalMachine = 2
    SPDKL_CurrentConfig = 5


SPAUDIOSTATE = _SPAUDIOSTATE
SPSTREAMFORMATTYPE = SPWAVEFORMATTYPE


__all__ = [
    'DISPID_SGRSTText', 'DISPID_SOTSetId', 'ISpeechRecoResultTimes',
    'SpeechRunState', 'SRTEmulated', 'DISPID_SRGId',
    'DISPID_SpeechCustomStream', 'DISPID_SPEEngineConfidence',
    'ISpStreamFormatConverter', 'SVP_8', 'SpeechAllElements',
    'ISpeechAudio', 'DISPID_SRGCmdLoadFromMemory',
    'SDA_One_Trailing_Space', 'SPAS_STOP', 'SPEI_PROPERTY_NUM_CHANGE',
    'SRTAutopause', 'DISPID_SRGIsPronounceable',
    'SpeechDisplayAttributes', 'DISPID_SGRSTPropertyId',
    'SpeechEngineConfidence', 'DISPID_SASState', 'SPPS_Noncontent',
    'SAFTNoAssignedFormat', 'eLEXTYPE_PRIVATE1', 'SFTSREngine',
    'SAFT48kHz8BitMono', 'SRTSMLTimeout',
    'SAFTCCITT_uLaw_22kHzStereo', 'DISPID_SPERetainedSizeBytes',
    'SP_VISEME_21', 'STSF_FlagCreate',
    '__MIDL___MIDL_itf_sapi_0000_0020_0002', 'DISPID_SVEPhoneme',
    'SpeechVoiceSpeakFlags', 'ISpeechTextSelectionInformation',
    'STCInprocServer', 'eLEXTYPE_PRIVATE15',
    'SAFTCCITT_ALaw_11kHzMono', 'SREPropertyStringChange',
    'DISPID_SpeechPhraseBuilder', 'DISPID_SpeechAudio',
    'DISPID_SPIGetDisplayAttributes', 'SPRST_INACTIVE_WITH_PURGE',
    'DISPID_SVEAudioLevel', 'DISPID_SRRGetXMLErrorInfo',
    'SPRST_ACTIVE', 'SGDSActiveWithAutoPause', 'DISPID_SWFEExtraData',
    'DISPID_SLRemovePronunciation', 'SPLOADOPTIONS',
    'DISPID_SGRsFindRule', 'SITooSlow', 'SPPS_Noun',
    'SpeechRecoProfileProperties', 'DISPID_SPAsCount',
    'SpeechTokenShellFolder', 'DISPIDSPRG', 'SAFT11kHz8BitStereo',
    'SPEI_SENTENCE_BOUNDARY', 'DISPID_SPRulesCount', 'SPSHORTCUTPAIR',
    'DISPID_SASCurrentDevicePosition', 'eWORDTYPE_DELETED',
    'SPPHRASEPROPERTY', 'ISpeechRecoResult', 'SAFT32kHz16BitStereo',
    'ISpNotifyTranslator', 'DISPID_SOTCategory',
    'SAFT24kHz8BitStereo', 'SpNullPhoneConverter',
    'SREPropertyNumChange', 'SRSEIsSpeaking', 'eLEXTYPE_RESERVED7',
    'DISPID_SVVoice', 'WAVEFORMATEX', 'SVEAudioLevel',
    'DISPID_SBSFormat', 'DISPID_SWFEAvgBytesPerSec', 'SPBO_PAUSE',
    'SAFTADPCM_11kHzMono', 'SPSNotOverriden', 'SVEEndInputStream',
    'STSF_CommonAppData', 'DISPID_SOTGetAttribute',
    'DISPID_SPIAudioSizeBytes', 'DISPID_SRProfile', 'SGRSTTRule',
    'SASStop', 'SpeechMicTraining', 'SREPhraseStart',
    'DISPID_SMSAMMHandle', 'ISpeechCustomStream', 'SpMMAudioIn',
    'SPGRAMMARWORDTYPE', 'DISPID_SPCLangId', 'UINT_PTR',
    'SPINTERFERENCE_NONE', 'DISPID_SRGCmdSetRuleIdState',
    'ISpeechLexiconWord', 'DISPID_SRRTimes',
    'DISPID_SpeechLexiconProns', 'DISPID_SPAs_NewEnum', 'SP_VISEME_1',
    'SAFTCCITT_uLaw_11kHzStereo', 'ISpeechObjectToken',
    'DISPID_SPRuleNumberOfElements', 'SAFT44kHz8BitStereo',
    'SAFT48kHz16BitMono', 'SP_VISEME_0', 'DISPID_SDKSetLongValue',
    'SPPS_RESERVED4', 'DISPID_SVResume', 'DISPID_SRCEInterference',
    'SPFM_NUM_MODES', 'DISPID_SGRSAddSpecialTransition', 'SRAONone',
    'SBOPause', 'DISPIDSPTSI', 'SAFTADPCM_11kHzStereo',
    'SAFT11kHz16BitStereo', 'DISPID_SRRSpeakAudio',
    'ISpeechVoiceStatus', 'SPDATAKEYLOCATION', 'SpeechAddRemoveWord',
    'DISPID_SpeechAudioStatus', 'DISPID_SRRTOffsetFromStart',
    'SPSHT_NotOverriden', 'SDTAll', 'DISPID_SPIAudioStreamPosition',
    'SPDKL_CurrentConfig', 'DISPID_SLPPartOfSpeech',
    'ISpeechRecoResult2', 'SAFT8kHz8BitMono', 'SPAUDIOOPTIONS',
    'DISPID_SASFreeBufferSpace', 'SSFMOpenForRead',
    'SAFTExtendedAudioFormat', 'SPSHORTCUTTYPE', 'SVP_17',
    'DISPID_SAFGetWaveFormatEx', 'SPEI_UNDEFINED',
    'DISPID_SVIsUISupported', 'SVP_10', 'DISPID_SVPause',
    'DISPID_SPIElements', 'DISPID_SpeechPhraseAlternate',
    'DISPID_SRCEPropertyNumberChange',
    'DISPID_SRSCurrentStreamNumber', 'DISPID_SRRAudio',
    'SPINTERFERENCE_NOISE', 'DISPID_SPRFirstElement', 'SRSEDone',
    'SpeechAudioFormatGUIDWave', 'SP_VISEME_6',
    'SpeechBookmarkOptions', 'SGSDisabled', 'SRATopLevel',
    'DISPID_SOTsItem', 'ISpeechRecognizer', 'eLEXTYPE_PRIVATE18',
    'SpeechVoiceSkipTypeSentence', 'SPPS_Interjection',
    'SPEI_RESERVED5', 'SVPAlert', 'SPFM_CREATE_ALWAYS',
    'SPCONTEXTSTATE', 'SAFT22kHz16BitStereo', 'SPAR_High',
    'SVSFIsXML', 'DISPID_SGRsCommitAndSave', '_RemotableHandle',
    'DISPID_SVAllowAudioOuputFormatChangesOnNextSet', 'SPAS_PAUSE',
    'ISpRecoContext', 'SPRS_ACTIVE_WITH_AUTO_PAUSE',
    'DISPID_SAEventHandle', 'SVP_20', 'DISPID_SABIBufferSize',
    'DISPID_SPRuleId', 'ISpStream', 'SPINTERFERENCE_LATENCY_WARNING',
    'DISPID_SPRsCount', 'SPSModifier', 'eLEXTYPE_RESERVED10',
    'SDTAlternates', 'SPEI_START_SR_STREAM', 'SPPS_SuppressWord',
    'DISPIDSPTSI_SelectionLength', 'DISPID_SRGetFormat',
    'SPXMLRESULTOPTIONS', 'DISPID_SPIAudioSizeTime', 'ISpAudio',
    'DISPID_SpeechVoice', 'DISPID_SRGRecoContext', 'DISPID_SPPName',
    'ISpResourceManager', 'SpMemoryStream', 'SPEI_PHONEME',
    'DISPID_SpeechRecoContextEvents', 'ISpeechPhraseElement',
    'DISPID_SPEAudioSizeTime', 'SGDisplay',
    'DISPID_SVSInputSentencePosition', 'SpeechRecoContextState',
    'DISPID_SpeechWaveFormatEx', 'eLEXTYPE_PRIVATE8',
    'SDTLexicalForm', 'SAFTCCITT_ALaw_44kHzStereo', 'SVSFIsNotXML',
    'DISPID_SRIsShared', 'SAFTCCITT_uLaw_8kHzStereo',
    'DISPID_SLWWord', 'eLEXTYPE_PRIVATE14', 'DISPID_SVEVoiceChange',
    'ISpeechPhraseReplacements', 'eLEXTYPE_PRIVATE4',
    'DISPID_SPEsItem', 'DISPID_SPEDisplayText', 'SAFTADPCM_44kHzMono',
    'SpeechCategoryAppLexicons', 'ISpDataKey', 'SAFT24kHz8BitMono',
    'SREAudioLevel', 'SAFTCCITT_ALaw_44kHzMono',
    'DISPID_SPIReplacements', 'SGSEnabled', 'SVSFlagsAsync',
    'SPWORDPRONUNCIATIONLIST', 'SRADynamic', 'SPSVerb',
    'SpeechAudioProperties', 'DISPID_SMSGetData', 'SPEI_VISEME',
    'SVSFUnusedFlags', 'SPEI_RESERVED3', 'SPEI_RECOGNITION',
    'SREAllEvents', 'SVEStartInputStream', 'SAFTCCITT_uLaw_8kHzMono',
    'SpMMAudioEnum', 'SAFTCCITT_uLaw_11kHzMono',
    'DISPID_SPRuleEngineConfidence', 'SPSHORTCUTPAIRLIST',
    'ISpXMLRecoResult', 'DISPID_SRRGetXMLResult',
    'DISPID_SPISaveToMemory', 'DISPID_SPRDisplayAttributes',
    'SREStateChange', 'ISpeechFileStream', 'DISPID_SRRTStreamTime',
    'DISPID_SGRsItem', 'SPCT_SUB_DICTATION', 'DISPID_SVPriority',
    'DISPID_SpeechPhraseRule', 'SGRSTTDictation',
    'DISPID_SRSClsidEngine', 'SPSMF_SAPI_PROPERTIES', 'SPAS_RUN',
    'Speech_StreamPos_Asap', 'SAFT16kHz8BitStereo',
    'ISpeechPhraseProperties', 'SDKLDefaultLocation', 'SVP_5',
    'SpeechFormatType', 'ISpProperties',
    'SPINTERFERENCE_LATENCY_TRUNCATE_END', 'ISpShortcut',
    'SAFT12kHz16BitMono', 'ISpeechGrammarRule', 'DISPID_SLWs_NewEnum',
    'SLODynamic', 'SpObjectTokenCategory', 'eLEXTYPE_PRIVATE17',
    'DISPID_SRCEEndStream', 'DISPID_SRCResume',
    'SPSMF_SRGS_SEMANTICINTERPRETATION_MS',
    'ISpeechObjectTokenCategory', 'DISPID_SVVolume',
    'SpSharedRecognizer', 'DISPID_SVGetAudioOutputs',
    'ISpSerializeState', 'DISPID_SRAudioInput', 'SPRULE',
    'IInternetSecurityManager', 'DISPID_SPRs_NewEnum', 'SPGS_ENABLED',
    'DISPID_SPPsCount', 'SAFTADPCM_8kHzStereo',
    'DISPID_SPRules_NewEnum', 'SpeechWordType',
    'DISPID_SpeechAudioBufferInfo', 'DISPID_SVStatus',
    'SPFM_OPEN_READWRITE', 'SAFT48kHz8BitStereo',
    'DISPID_SVEventInterests', 'DISPID_SDKSetBinaryValue',
    'SPVOICESTATUS', 'DISPID_SGRInitialState', 'DISPID_SGRsCommit',
    'DISPID_SASCurrentSeekPosition', 'DISPID_SGRSTsItem',
    'DISPID_SGRSRule', 'SVP_11', 'SRESoundStart', 'ISpMMSysAudio',
    'SPSERIALIZEDPHRASE', 'DISPID_SVGetProfiles',
    'DISPID_SLPs_NewEnum', 'ISpObjectToken', 'SVSFParseSapi',
    'SECFIgnoreKanaType', 'SpeechVoicePriority',
    'DISPID_SPILanguageId', 'SPEI_MIN_TTS', 'DISPID_SGRSTs_NewEnum',
    'DISPID_SPAStartElementInResult', 'SDTPronunciation',
    'DISPID_SPPsItem', 'DISPID_SVSpeakStream', 'SDKLLocalMachine',
    'SPEI_RESERVED1', 'SRTReSent', 'SSSPTRelativeToEnd',
    'DISPID_SpeechLexicon', 'ISpeechPhraseAlternates', 'SFTInput',
    'SPEI_END_INPUT_STREAM', 'DISPID_SVDisplayUI',
    'DISPID_SLAddPronunciation', 'SAFTGSM610_44kHzMono',
    'SPBOOKMARKOPTIONS', 'ISpPhoneConverter',
    'DISPID_SPIRetainedSizeBytes', 'SPADAPTATIONRELEVANCE',
    'eLEXTYPE_PRIVATE20', 'SPXRO_SML', 'SPAUDIOSTATE',
    'SPRECOGNIZERSTATUS', 'SPGS_EXCLUSIVE', 'SpeechAudioVolume',
    'SECFIgnoreWidth', 'DISPID_SPELexicalForm',
    'DISPID_SVAudioOutput', 'SP_VISEME_8', 'SVEViseme',
    'DISPID_SRGetPropertyNumber', 'SDA_Two_Trailing_Spaces',
    'SAFT16kHz8BitMono', 'DISPID_SGRSTPropertyValue',
    'DISPID_SRGCmdSetRuleState', 'DISPID_SGRSTsCount',
    'SVSFIsFilename', 'SAFTADPCM_44kHzStereo',
    'SpeechCategoryPhoneConverters', 'SP_VISEME_3',
    'SPEI_SR_RETAINEDAUDIO', 'SPRS_ACTIVE', 'SpeechTokenContext',
    'IInternetSecurityMgrSite', 'DISPID_SRGDictationUnload',
    'Library', 'SPWORDPRONUNCIATION', 'SAFTNonStandardFormat',
    'SPSERIALIZEDRESULT', 'DISPIDSPTSI_ActiveOffset', 'SVP_15',
    'SVEPrivate', 'DISPID_SPEPronunciation', 'DISPID_SPPConfidence',
    'DISPID_SASetState', 'eLEXTYPE_PRIVATE5', 'DISPID_SGRSTRule',
    'SVP_12', 'SGPronounciation', 'DISPID_SVSRunningState',
    'ISpRecoGrammar', 'DISPID_SWFEChannels',
    'DISPID_SRCSetAdaptationData', 'SP_VISEME_10',
    'DISPID_SRRAudioFormat', 'SWTDeleted', 'SPEI_SOUND_START',
    'DISPID_SVRate', 'SPRECOCONTEXTSTATUS', 'SPLO_STATIC',
    'SPGRAMMARSTATE', 'Speech_Max_Pron_Length',
    'DISPIDSPTSI_ActiveLength', 'DISPID_SpeechXMLRecoResult',
    'ISpGrammarBuilder', 'SPWORDTYPE', 'DISPID_SpeechMMSysAudio',
    'DISPID_SRGetRecognizers', 'SpObjectToken', 'SP_VISEME_14',
    'SPEI_TTS_BOOKMARK', 'SPEI_END_SR_STREAM', 'eLEXTYPE_PRIVATE16',
    'SRCS_Enabled', 'DISPID_SLGetPronunciations', 'DISPID_SBSSeek',
    'ISpPhraseAlt', 'SREAdaptation', 'ISpeechPhraseInfoBuilder',
    'ISpeechPhraseReplacement', 'DISPID_SOTGetDescription',
    'SAFTCCITT_ALaw_8kHzStereo', 'SGRSTTWildcard',
    'DISPID_SRCESoundEnd', 'SPEI_SR_PRIVATE',
    'SpeechEngineProperties', 'SPPS_LMA', 'ISpeechLexiconWords',
    'typelib_path', 'DISPID_SpeechGrammarRuleStateTransitions',
    'ISpRecoCategory', 'DISPID_SGRSTPropertyName', 'SPBINARYGRAMMAR',
    'SVSFNLPSpeakPunc', 'DISPID_SpeechObjectTokens', 'SREStreamStart',
    'eLEXTYPE_LETTERTOSOUND', 'SPPS_NotOverriden', 'SPCT_DICTATION',
    '_SPAUDIOSTATE', 'SVP_4', 'DISPID_SOTDataKey', 'SVP_16',
    'SPEI_MAX_SR', 'LONG_PTR', 'SpeechLoadOption', 'SLOStatic',
    'SAFT12kHz16BitStereo', 'eLEXTYPE_PRIVATE9',
    'DISPID_SPPBRestorePhraseFromMemory',
    'DISPID_SPRNumberOfElements', 'DISPID_SLPLangId',
    'SpeechRecoEvents', 'SpeechGrammarTagWildcard', 'SPPHRASEELEMENT',
    'SPGS_DISABLED', 'DISPID_SGRClear', 'DISPID_SRRSaveToMemory',
    'DISPID_SRCERequestUI', 'SECNormalConfidence',
    'DISPID_SRSetPropertyString', 'SPAR_Low', 'SINoSignal',
    'ISpeechPhraseRules', 'SPPROPERTYINFO',
    'SpeechSpecialTransitionType', 'DISPID_SDKDeleteKey',
    'SpeechRegistryLocalMachineRoot', 'SPBO_NONE',
    'DISPID_SRSSupportedLanguages', 'DISPID_SRGRules',
    'ISpeechPhraseElements', 'SPPS_Function', 'SAFT44kHz16BitMono',
    'DISPID_SRRTLength', 'DISPID_SRCEFalseRecognition',
    'DISPID_SCSBaseStream', 'DISPID_SRCVoicePurgeEvent',
    'SAFT24kHz16BitStereo', '__MIDL_IWinTypes_0009',
    'DISPID_SPAsItem', 'DISPID_SPEAudioStreamOffset',
    'DISPID_SOTCreateInstance', 'DISPID_SDKSetStringValue',
    'SpeechVisemeFeature', 'SLTUser', 'SVP_2', 'SVSFParseSsml',
    'DISPID_SRCEStartStream', 'SINone', 'SRSInactiveWithPurge',
    'DISPID_SPIGetText', 'DISPID_SABIMinNotification',
    'eLEXTYPE_USER', 'SPPHRASERULE', 'SECFDefault', 'SP_VISEME_20',
    'DISPID_SRCCreateGrammar', 'DISPID_SPEsCount',
    'SpeechCategoryAudioIn', 'DISPID_SWFEBitsPerSample',
    'DISPID_SRGReset', 'SpPhoneConverter', 'SPEI_TTS_PRIVATE',
    'SpeechTokenKeyUI', 'DISPID_SRCEHypothesis', 'SECLowConfidence',
    'SRERequestUI', 'DISPID_SLAddPronunciationByPhoneIds',
    'SpInProcRecoContext', 'IEnumSpObjectTokens',
    'SPWP_UNKNOWN_WORD_PRONOUNCEABLE', 'SPRS_ACTIVE_USER_DELIMITED',
    'SPEI_WORD_BOUNDARY', 'DISPID_SASNonBlockingIO', 'SPCT_SLEEP',
    'SDTAudio', 'SSSPTRelativeToCurrentPosition', 'SAFT44kHz8BitMono',
    'SGLexical', 'eLEXTYPE_PRIVATE7', 'SPWT_PRONUNCIATION',
    'SpeechAudioFormatGUIDText', 'DISPID_SGRAttributes',
    'SPFM_OPEN_READONLY', 'SDA_No_Trailing_Space', 'eWORDTYPE_ADDED',
    'eLEXTYPE_USER_SHORTCUT', 'SPTEXTSELECTIONINFO',
    'DISPID_SVEViseme', 'DISPID_SPANumberOfElementsInResult',
    'DISPID_SPPFirstElement', 'SP_VISEME_17', 'SpeechInterference',
    'ISpPhrase', 'ISpeechPhraseRule', 'SPCT_COMMAND', 'SP_VISEME_16',
    'SpeechAudioFormatType', 'ISpStreamFormat', 'IEnumString',
    'DISPID_SRCBookmark', 'SAFT8kHz16BitStereo',
    'DISPID_SPCIdToPhone', 'SPEI_PROPERTY_STRING_CHANGE',
    'SPEI_SR_BOOKMARK', 'SP_VISEME_13', 'SAFT11kHz8BitMono',
    'DISPID_SPARecoResult', 'DISPID_SGRSTWeight',
    'SPSMF_SRGS_SEMANTICINTERPRETATION_W3C', 'SDTReplacement',
    'DISPID_SVEWord', 'DISPID_SOTCDefault', 'DISPID_SpeechDataKey',
    'SPVPRI_OVER', 'SPSHT_Unknown', 'DISPID_SpeechRecoResult2',
    'SAFT48kHz16BitStereo', 'SPRS_INACTIVE', 'SGSExclusive',
    'SVSFPersistXML', 'DISPID_SRCEventInterests',
    'SpeechDictationTopicSpelling', 'DISPID_SDKCreateKey',
    'DISPID_SRCreateRecoContext', 'SpeechStreamFileMode',
    'ISpeechRecoGrammar', 'SpeechUserTraining',
    'DISPID_SRAudioInputStream', 'DISPID_SpeechGrammarRules',
    'SPRST_NUM_STATES', 'DISPID_SPEs_NewEnum', 'SpPhraseInfoBuilder',
    'DISPID_SDKEnumKeys', 'ISpeechRecognizerStatus',
    'DISPID_SVSLastResult', 'DISPID_SRCERecognizerStateChange',
    'SP_VISEME_18', 'SpPhoneticAlphabetConverter',
    'ISpeechPhraseAlternate', 'DISPID_SpeechMemoryStream',
    'DISPID_SVEStreamStart', 'DISPID_SRDisplayUI', 'DISPID_SOTCId',
    'SREPrivate', 'DISPID_SPCPhoneToId', 'SAFTCCITT_ALaw_22kHzMono',
    'SPAS_CLOSED', 'SREStreamEnd', 'DISPID_SDKEnumValues',
    'ISpLexicon', 'DISPID_SOTId', 'ISpeechDataKey',
    'DISPID_SPPNumberOfElements', 'DISPID_SGRAddState',
    'DISPID_SRIsUISupported', 'SAFT44kHz16BitStereo',
    'SpWaveFormatEx', 'SVP_3', 'SpStreamFormatConverter',
    'DISPIDSPTSI_SelectionOffset', 'SpLexicon', 'SASClosed',
    'ISpNotifySource', 'SpeechCategoryAudioOut', 'SVSFDefault',
    'tagSPTEXTSELECTIONINFO', 'DISPID_SDKGetBinaryValue',
    'ISpeechObjectTokens', 'ISpRecoResult', 'DISPID_SPACommit',
    'SPEI_SOUND_END', 'DISPID_SPERetainedStreamOffset',
    'DISPID_SRGCmdLoadFromProprietaryGrammar', 'DISPID_SLPPhoneIds',
    'SPCS_ENABLED', 'SVP_18', 'SPFILEMODE', 'SPEI_MIN_SR',
    'SPSTREAMFORMATTYPE', 'eLEXTYPE_APP', 'SPWT_DISPLAY',
    'ISpeechAudioFormat', 'DISPID_SpeechAudioFormat',
    'SDKLCurrentConfig', 'DISPID_SVGetVoices',
    'DISPID_SRSAudioStatus', 'SpeechPartOfSpeech',
    'eLEXTYPE_PRIVATE13', 'SPVISEMES', 'SPSHT_EMAIL', 'SBONone',
    'DISPID_SRCAudioInInterferenceStatus', 'DISPID_SPEAudioSizeBytes',
    'DISPID_SOTRemove', 'SPEI_ADAPTATION',
    'DISPID_SOTRemoveStorageFileName', 'SRADefaultToActive',
    'SPEI_PHRASE_START', 'SP_VISEME_9', 'SAFT22kHz16BitMono',
    'DISPID_SVSpeak', 'DISPID_SOTCSetId', 'SPEVENTSOURCEINFO',
    'SPRST_ACTIVE_ALWAYS', 'SPWORDPRONOUNCEABLE',
    'DISPID_SPERequiredConfidence', 'DISPID_SPRuleParent',
    'ISpRecoContext2', 'SGLexicalNoSpecialChars',
    'DISPID_SVEStreamEnd', 'DISPID_SRGSetTextSelection', 'SSFMCreate',
    'SDKLCurrentUser', 'SpVoice', 'SpCompressedLexicon',
    'SPSInterjection', 'DISPID_SpeechPhraseElements',
    'ISpeechMemoryStream', 'SAFT22kHz8BitStereo',
    'DISPID_SpeechPhraseReplacements', 'SPWAVEFORMATTYPE',
    'SSSPTRelativeToStart', 'DISPID_SVAlertBoundary',
    'SpeechRecognitionType', 'SINoise', 'DISPID_SpeechLexiconWord',
    'SVP_9', 'DISPID_SOTGetStorageFileName', 'SGRSTTTextBuffer',
    'SPINTERFERENCE', 'SPEI_RESERVED2', 'STSF_AppData',
    'STCInprocHandler', 'SPINTERFERENCE_TOOSLOW',
    'eLEXTYPE_RESERVED9', 'DISPID_SRSNumberOfActiveRules',
    'ISpRecognizer3', 'ISpeechLexiconPronunciations', 'ISpRecognizer',
    'SPINTERFERENCE_NOSIGNAL', 'DISPID_SpeechPhraseElement',
    'SpeechCategoryRecoProfiles', 'SRSActiveAlways',
    'SPWP_UNKNOWN_WORD_UNPRONOUNCEABLE', 'SPVPRI_NORMAL',
    'eLEXTYPE_RESERVED8', 'SRAORetainAudio',
    'SPWT_LEXICAL_NO_SPECIAL_CHARS', 'ISpeechResourceLoader',
    'DISPID_SpeechRecoContext', 'SP_VISEME_2',
    'ISpeechPhraseProperty', 'SVSFPurgeBeforeSpeak', 'SRAInterpreter',
    'SAFT12kHz8BitMono', 'DISPID_SRRRecoContext',
    'ISpeechLexiconPronunciation', 'eLEXTYPE_PRIVATE19',
    'DISPID_SpeechObjectTokenCategory', 'SPWT_LEXICAL',
    'SRERecoOtherContext', 'SAFT32kHz16BitMono', 'SPAR_Medium',
    'SPCS_DISABLED', 'SRSActive', 'DISPID_SRGCmdLoadFromFile',
    'SpeechPropertyResourceUsage', 'ISpeechXMLRecoResult', 'SVPOver',
    'IStream', 'DISPID_SpeechFileStream', 'DISPID_SVWaitUntilDone',
    'SpCustomStream', 'DISPID_SpeechPhraseRules', 'SPEVENTENUM',
    'DISPID_SRCVoice', 'SSFMOpenReadWrite',
    'DISPID_SLRemovePronunciationByPhoneIds', 'ISpeechLexicon',
    'SpShortcut', 'SpeechPropertyLowConfidenceThreshold',
    'DISPID_SWFEFormatTag', 'SAFT32kHz8BitMono',
    'DISPID_SpeechGrammarRuleState', 'DISPID_SRCEAudioLevel',
    'SPDKL_DefaultLocation', 'SRARoot', 'DISPID_SPIEnginePrivateData',
    'ISpRecoGrammar2', 'SREFalseRecognition', 'SP_VISEME_19',
    'SpUnCompressedLexicon', 'SpFileStream', 'SAFT24kHz16BitMono',
    'SPSFunction', 'ISpNotifySink', 'SpeechRegistryUserRoot',
    'DISPID_SpeechRecoResultTimes', 'DISPID_SVSInputWordPosition',
    'SPCT_SUB_COMMAND', 'SVEPhoneme', 'SGDSActive', 'DISPID_SLPsItem',
    'SpeechRetainedAudioOptions', 'SPRECOSTATE',
    'Speech_Max_Word_Length', 'DISPID_SWFESamplesPerSec',
    'DISPID_SVSCurrentStreamNumber', 'SPEI_MAX_TTS',
    'eLEXTYPE_PRIVATE12', 'SVPNormal', 'DISPID_SVSkip',
    'SECHighConfidence', 'DISPID_SVEEnginePrivate',
    'SPINTERFERENCE_TOOLOUD', 'SAFT12kHz8BitStereo', 'SPWF_INPUT',
    'SITooFast', 'DISPID_SPEAudioTimeOffset', 'SVSFParseMask',
    'ISpeechAudioBufferInfo', 'DISPID_SPEActualConfidence',
    'ISpeechMMSysAudio', 'DISPID_SRRPhraseInfo', 'SDTRule',
    'DISPID_SVSLastBookmark', 'DISPID_SRCRetainedAudio',
    'DISPID_SpeechPhraseReplacement', 'DISPID_SLWLangId',
    'ISpRecognizer2', 'DISPID_SpeechRecognizerStatus',
    'SAFTCCITT_uLaw_22kHzMono', 'Speech_Default_Weight',
    'SSTTDictation', 'SPCATEGORYTYPE', 'SDTDisplayText',
    'SGRSTTEpsilon', 'ISpeechPhraseInfo', 'DISPID_SPPs_NewEnum',
    'DISPID_SLWPronunciations',
    'SpeechPropertyHighConfidenceThreshold',
    'SPINTERFERENCE_LATENCY_TRUNCATE_BEGIN', 'DISPID_SPIEngineId',
    'SPSSuppressWord', 'SpeechPropertyResponseSpeed',
    'DISPID_SPPParent', 'SPRULESTATE', 'SpeechLexiconType',
    'DISPID_SVSLastBookmarkId', 'DISPID_SRRDiscardResultInfo',
    'SRERecognition', 'DISPID_SREmulateRecognition',
    'DISPID_SRRSetTextFeedback', 'DISPID_SpeechPhraseProperty',
    'DISPID_SPRuleFirstElement', 'SVSFParseAutodetect',
    'SPEI_ACTIVE_CATEGORY_CHANGED', 'SAFT16kHz16BitStereo',
    'DISPID_SLGetGenerationChange', 'DISPID_SPRulesItem',
    'DISPID_SpeechGrammarRuleStateTransition', 'SAFT8kHz8BitStereo',
    'DISPID_SPPValue', '__MIDL___MIDL_itf_sapi_0000_0020_0001',
    'SPPS_RESERVED3', 'DISPID_SpeechPhoneConverter',
    'SPEI_HYPOTHESIS', 'SITooQuiet', 'SpeechGrammarWordType',
    'SAFTGSM610_22kHzMono', 'DISPID_SPIGrammarId',
    'DISPID_SVSPhonemeId', 'DISPID_SPRuleConfidence',
    'SPEI_INTERFERENCE', 'DISPID_SLGenerationId', 'SLTApp',
    'DISPID_SpeechVoiceStatus', 'ISpeechBaseStream', 'SPFM_CREATE',
    'SPEI_RESERVED6', 'DISPID_SABufferNotifySize',
    'SECFEmulateResult', 'SSTTWildcard', 'SVF_None',
    'DISPID_SVSLastStreamNumberQueued', 'SAFTCCITT_ALaw_8kHzMono',
    'SRSInactive', 'DISPID_SRCEPhraseStart',
    'SpeechEmulationCompareFlags', 'SWTAdded', 'DISPID_SRCPause',
    'ISpeechGrammarRuleStateTransition', 'STCAll',
    'ISpObjectWithToken', 'SASPause', 'SP_VISEME_12', 'SPWORD',
    'SWPUnknownWordPronounceable', 'SVESentenceBoundary',
    'SECFIgnoreCase', 'DISPID_SGRSTType', 'SpeechTokenValueCLSID',
    'SAFTText', 'SPAUDIOBUFFERINFO', 'eLEXTYPE_VENDORLEXICON',
    'SVP_21', 'SpeechDiscardType', 'SGRSTTWord',
    'DISPID_SABIEventBias', 'DISPID_SRState', 'DISPID_SAVolume',
    'SPEI_START_INPUT_STREAM', 'SP_VISEME_15',
    'SPEI_RECO_STATE_CHANGE', 'SPAO_RETAIN_AUDIO',
    'DISPID_SRGDictationLoad', 'SPINTERFERENCE_TOOQUIET',
    'DISPID_SpeechObjectToken', 'SPVPRIORITY', 'DISPID_SOTDisplayUI',
    'DISPID_SpeechRecognizer', 'SPAR_Unknown', 'DISPID_SMSSetData',
    'DISPID_SADefaultFormat', 'DISPID_SRCRequestedUIType',
    'SPAUDIOSTATUS', 'SASRun', 'DISPID_SOTCGetDataKey', 'SPPS_Verb',
    'DISPID_SFSOpen', 'SP_VISEME_7',
    'SpeechGrammarRuleStateTransitionType',
    'DISPID_SRCCmdMaxAlternates', 'SPLEXICONTYPE',
    'DISPID_SRRecognizer', 'DISPID_SLPSymbolic', 'DISPID_SPRsItem',
    'SPDKL_CurrentUser', 'SWPKnownWordPronounceable', 'tagSTATSTG',
    'ISpEventSink', 'SAFT11kHz16BitMono', 'SPRECORESULTTIMES',
    'eLEXTYPE_PRIVATE3', 'DISPID_SVEBookmark', 'SPSMF_UPS',
    'DISPID_SLPType', 'DISPID_SRRAlternates', 'tagSPPROPERTYINFO',
    'DISPID_SBSRead', 'DISPID_SRSetPropertyNumber',
    'SAFTGSM610_8kHzMono', 'DISPID_SRCEBookmark',
    'SpeechWordPronounceable', 'SVEWordBoundary',
    'DISPID_SPEDisplayAttributes', 'DISPID_SpeechPhraseInfo',
    'SpeechVoiceEvents', 'SVSFNLPMask',
    'DISPID_SVSInputSentenceLength', 'SPVPRI_ALERT',
    'DISPID_SGRsCount', 'SAFT8kHz16BitMono', 'DISPID_SPRuleChildren',
    'SREBookmark', 'DISPID_SRCEAdaptation', 'DISPID_SRCRecognizer',
    'SPPHRASE', 'SpeechCategoryVoices', 'DISPID_SVSVisemeId',
    'DISPID_SpeechRecoResult', 'SRESoundEnd',
    'SpeechTokenIdUserLexicon', 'eLEXTYPE_RESERVED6',
    'SAFTTrueSpeech_8kHz1BitMono', 'SGDSInactive',
    'DISPID_SPRuleName', 'SPWF_SRENGINE', 'DISPID_SGRSTNextState',
    'DISPID_SPAPhraseInfo', 'SpeechVisemeType', 'SRTStandard',
    'SpeechRuleAttributes', 'DISPID_SGRSAddRuleTransition',
    'DISPID_SVSyncronousSpeakTimeout',
    'DISPID_SRCCreateResultFromMemory', 'DISPID_SRGCmdLoadFromObject',
    'DISPID_SRSCurrentStreamPosition', 'SECFNoSpecialChars',
    'DISPID_SpeechLexiconWords', 'ISpEventSource',
    'DISPID_SLGetWords', 'ISpeechGrammarRuleStateTransitions',
    'SAFT22kHz8BitMono', 'DISPID_SGRAddResource',
    'DISPID_SOTCEnumerateTokens', 'SVP_13',
    'Speech_StreamPos_RealTime', 'SpeechAudioState',
    'SPPHRASEREPLACEMENT', 'SPEI_FALSE_RECOGNITION',
    'SAFTCCITT_ALaw_22kHzStereo', 'SpSharedRecoContext',
    'DISPID_SBSWrite', 'ISpeechWaveFormatEx',
    'DISPID_SGRSAddWordTransition', 'DISPID_SLWsItem',
    'DISPID_SRAllowAudioInputFormatChangesOnNextSet',
    'DISPID_SLWsCount', 'ISpeechVoice', 'SAFT16kHz16BitMono',
    'STSF_LocalAppData', 'SAFTCCITT_ALaw_11kHzStereo',
    'ISpeechGrammarRules', 'SPSUnknown', 'DISPID_SRCESoundStart',
    'SpeechRecognizerState', 'SAFTADPCM_8kHzMono',
    'SpeechPropertyNormalConfidenceThreshold',
    'SAFTADPCM_22kHzStereo', 'SpInprocRecognizer',
    'DISPID_SABufferInfo', 'SPPARTOFSPEECH',
    'SWPUnknownWordUnpronounceable', 'SSFMCreateForWrite',
    'DISPID_SVSInputWordLength', 'SVP_0', 'SVSFVoiceMask',
    'SPAO_NONE', 'SP_VISEME_4', 'SDTProperty',
    'DISPID_SOTIsUISupported', 'SPXRO_Alternates_SML',
    'SVEVoiceChange', 'DISPID_SpeechGrammarRule',
    'DISPID_SGRSTransitions', 'DISPID_SRRTTickCount', 'SITooLoud',
    'eLEXTYPE_PRIVATE11', 'SPBO_AHEAD', 'SVP_19', 'STCRemoteServer',
    'DISPID_SRAllowVoiceFormatMatchingOnNextSet', 'DISPID_SRCState',
    'SpeechStreamSeekPositionType', 'DISPID_SRGCmdLoadFromResource',
    'DISPID_SRCERecognitionForOtherContext',
    'SpeechPropertyComplexResponseSpeed', 'SPSLMA', 'DISPID_SPRText',
    'SAFTCCITT_uLaw_44kHzMono', 'STCLocalServer', 'DISPID_SOTsCount',
    'SPEI_VOICE_CHANGE', 'DISPID_SPIStartTime',
    'SDA_Consume_Leading_Spaces', 'SPEI_RECO_OTHER_CONTEXT',
    'SPWORDLIST', 'SpTextSelectionInformation', 'DISPID_SPPId',
    'ISpeechAudioStatus', 'SPSMF_SRGS_SAPIPROPERTIES',
    'SPINTERFERENCE_TOOFAST', 'DISPID_SDKGetlongValue',
    'SpMMAudioOut', 'SAFTCCITT_uLaw_44kHzStereo', 'SpResourceManager',
    'SPLO_DYNAMIC', 'DISPID_SRCERecognition', 'SVP_14', 'ISpVoice',
    'DISPID_SAFType', 'DISPID_SAStatus', 'SpeechTokenKeyAttributes',
    'DISPID_SVAudioOutputStream', 'DISPID_SpeechBaseStream',
    'DISPID_SPIRule', 'SPEI_REQUEST_UI', 'SpeechPropertyAdaptationOn',
    'SAFT32kHz8BitStereo', 'DISPID_SGRName', 'SpeechRuleState',
    'SPSHT_OTHER', 'SPSEMANTICERRORINFO', 'SP_VISEME_5', 'SVP_7',
    'DISPID_SDKGetStringValue', 'DISPID_SGRId',
    'DISPID_SRGDictationSetState', 'ISpeechRecoContext',
    'DISPID_SRCEEnginePrivate', 'DISPID_SOTMatchesAttributes',
    'SPSEMANTICFORMAT', 'SPBO_TIME_UNITS',
    'SPWP_KNOWN_WORD_PRONOUNCEABLE', 'SAFTDefault',
    'DISPID_SRGSetWordSequenceData', 'DISPID_SRStatus', 'SVP_1',
    'DISPID_SPPEngineConfidence', 'SAFTGSM610_11kHzMono',
    'SSTTTextBuffer', 'eLEXTYPE_MORPHOLOGY', 'eLEXTYPE_PRIVATE6',
    'ISpeechGrammarRuleState', 'DISPID_SGRsAdd', 'SPPS_Unknown',
    'SRCS_Disabled', 'SpeechGrammarTagDictation',
    'DISPID_SAFSetWaveFormatEx', 'ISpeechPhoneConverter',
    'SPEI_TTS_AUDIO_LEVEL', 'SPPS_RESERVED1',
    'SpeechCategoryRecognizers', 'SP_VISEME_11', 'SRTExtendableParse',
    'DISPID_SVESentenceBoundary', 'DISPID_SpeechVoiceEvent',
    'DISPID_SRGState', 'DISPID_SPPChildren',
    'DISPID_SpeechPhraseProperties', 'SpeechVoiceCategoryTTSRate',
    'SPDKL_LocalMachine', 'DISPID_SWFEBlockAlign', 'SREHypothesis',
    'eLEXTYPE_PRIVATE2', 'DISPID_SGRs_NewEnum',
    'ISpObjectTokenCategory', 'DISPID_SpeechPhraseAlternates',
    'SVEAllEvents', 'eLEXTYPE_PRIVATE10', 'DISPID_SDKOpenKey',
    'SPEVENT', 'DISPID_SRCRetainedAudioFormat', 'SRAImport',
    'SpStream', 'DISPID_SMSADeviceId', 'SpAudioFormat',
    'ISpPhoneticAlphabetSelection', 'SPPS_RESERVED2',
    'DISPID_SVSpeakCompleteEvent', 'DISPID_SRGCommit',
    'DISPID_SpeechLexiconPronunciation', 'DISPID_SMSALineId',
    'DISPID_SPIProperties', 'ISpPhoneticAlphabetConverter',
    'DISPID_SDKDeleteValue', 'DISPID_SRCEPropertyStringChange',
    'SpNotifyTranslator', 'SPRST_INACTIVE',
    'SpeechGrammarTagUnlimitedDictation', 'DISPID_SLPsCount',
    'SAFTADPCM_22kHzMono', 'DISPID_SOTs_NewEnum', 'SREInterference',
    'SPPS_Modifier', 'SPSNoun', 'DISPID_SFSClose',
    'SpeechTokenKeyFiles', 'SPEI_SR_AUDIO_LEVEL', 'SVEBookmark',
    'SpeechGrammarState', 'SVF_Stressed', '_ISpeechRecoContextEvents',
    'SGDSActiveUserDelimited', 'DISPID_SLWType',
    'ISpeechRecoResultDispatch', 'DISPID_SAFGuid', 'SRAExport',
    'SVP_6', 'DISPID_SRGetPropertyString', 'SVF_Emphasis',
    'eLEXTYPE_RESERVED4', 'DISPID_SVGetAudioInputs',
    '_ISpeechVoiceEvents', 'DISPID_SGRsDynamic',
    'SpeechDataKeyLocation'
]

