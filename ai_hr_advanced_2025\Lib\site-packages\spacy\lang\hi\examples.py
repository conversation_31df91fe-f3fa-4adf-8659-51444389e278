"""
Example sentences to test spaCy and its language models.

>>> from spacy.lang.hi.examples import sentences
>>> docs = nlp.pipe(sentences)
"""


sentences = [
    "एप्पल 1 अरब डॉलर के लिए यू.के. स्टार्टअप खरीदने पर विचार कर रहा है।",
    "स्वायत्त कारें निर्माताओं की ओर बीमा दायित्व रखतीं हैं।",
    "सैन फ्रांसिस्को फुटपाथ वितरण रोबोटों पर प्रतिबंध लगाने का विचार कर रहा है।",
    "लंदन यूनाइटेड किंगडम का विशाल शहर है।",
    "आप कहाँ हो?",
    "फ्रांस के राष्ट्रपति कौन हैं?",
    "संयुक्त राज्यों की राजधानी क्या है?",
    "बराक ओबामा का जन्म कब हुआ था?",
    "जवाहरलाल नेहरू भारत के पहले प्रधानमंत्री हैं।",
    "राजेंद्र प्रसाद, भारत के पहले राष्ट्रपति, दो कार्यकाल के लिए कार्यालय रखने वाले एकमात्र व्यक्ति हैं।",
]
