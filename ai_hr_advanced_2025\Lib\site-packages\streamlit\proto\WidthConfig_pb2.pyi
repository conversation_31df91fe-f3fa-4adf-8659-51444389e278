"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
Common layout-related enums and messages that can be reused across different elements."""

import builtins
import google.protobuf.descriptor
import google.protobuf.message
import typing

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

@typing.final
class WidthConfig(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    USE_STRETCH_FIELD_NUMBER: builtins.int
    USE_CONTENT_FIELD_NUMBER: builtins.int
    PIXEL_WIDTH_FIELD_NUMBER: builtins.int
    use_stretch: builtins.bool
    use_content: builtins.bool
    pixel_width: builtins.int
    def __init__(
        self,
        *,
        use_stretch: builtins.bool = ...,
        use_content: builtins.bool = ...,
        pixel_width: builtins.int = ...,
    ) -> None: ...
    def <PERSON><PERSON>ield(self, field_name: typing.Literal["pixel_width", b"pixel_width", "use_content", b"use_content", "use_stretch", b"use_stretch", "width_spec", b"width_spec"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["pixel_width", b"pixel_width", "use_content", b"use_content", "use_stretch", b"use_stretch", "width_spec", b"width_spec"]) -> None: ...
    def WhichOneof(self, oneof_group: typing.Literal["width_spec", b"width_spec"]) -> typing.Literal["use_stretch", "use_content", "pixel_width"] | None: ...

global___WidthConfig = WidthConfig
