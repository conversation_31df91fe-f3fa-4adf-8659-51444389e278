"""
Example sentences to test spaCy and its language models.

>>> from spacy.lang.dsb.examples import sentences
>>> docs = nlp.pipe(sentences)
"""


sentences = [
    "Z tym stwori so wuměnjenje a zakład za dalše wobdźěłanje přez analyzu tekstoweje struktury a semantisku anotaciju a z tym tež za tu předstajenu digitalnu online-wersiju.",
    "Mi so tu jara derje spodoba.",
    "Kotre nowniny chceće měć?",
    "Tak ako w slědnem lěśe jo teke lětosa jano doma zapustowaś móžno.",
    "Zwóstanjo pótakem hyšći wjele źěła.",
]
