from ...attrs import LIKE_NUM

_num_words = [
    "нула",
    "едно",
    "един",
    "една",
    "две",
    "три",
    "четири",
    "пет",
    "шест",
    "седем",
    "осем",
    "девет",
    "десет",
    "единадесет",
    "единайсет",
    "дванадесет",
    "дванайсет",
    "тринадесет",
    "тринайсет",
    "четиринадесет",
    "четиринайсет",
    "петнадесет",
    "петнайсет",
    "шестнадесет",
    "шестнайсет",
    "седемнадесет",
    "седемнайсет",
    "осемнадесет",
    "осемнайсет",
    "деветнадесет",
    "деветнайсет",
    "двадесет",
    "двайсет",
    "тридесет",
    "трийсет",
    "четиридесет",
    "четиресет",
    "петдесет",
    "шестдесет",
    "шейсет",
    "седемдесет",
    "осемдесет",
    "деветдесет",
    "сто",
    "двеста",
    "триста",
    "четиристотин",
    "петстотин",
    "шестстотин",
    "седемстотин",
    "осемстотин",
    "деветстотин",
    "хиляда",
    "милион",
    "милиона",
    "милиард",
    "милиарда",
    "трилион",
    "трилионa",
    "билион",
    "билионa",
    "квадрилион",
    "квадрилионa",
    "квинтилион",
    "квинтилионa",
]


def like_num(text):
    if text.startswith(("+", "-", "±", "~")):
        text = text[1:]
    text = text.replace(",", "").replace(".", "")
    if text.isdigit():
        return True
    if text.count("/") == 1:
        num, denom = text.split("/")
        if num.isdigit() and denom.isdigit():
            return True
    if text.lower() in _num_words:
        return True
    return False


LEX_ATTRS = {LIKE_NUM: like_num}
