import{n as l,b4 as v,J as b,b5 as n,r as u,b6 as s,j as a,b7 as B,b8 as k,b9 as S}from"./index.C1z8KpLA.js";function x(o,r){switch(o){case n.XSMALL:return{padding:`${r.spacing.twoXS} ${r.spacing.sm}`,fontSize:r.fontSizes.sm};case n.SMALL:return{padding:`${r.spacing.twoXS} ${r.spacing.md}`};case n.LARGE:return{padding:`${r.spacing.md} ${r.spacing.md}`};default:return{padding:`${r.spacing.xs} ${r.spacing.md}`}}}const e=l("a",{target:"e2m15hl0"})(({containerWidth:o,size:r,theme:i})=>({display:"inline-flex",alignItems:"center",justifyContent:"center",fontWeight:i.fontWeights.normal,padding:`${i.spacing.xs} ${i.spacing.md}`,borderRadius:i.radii.default,minHeight:i.sizes.minElementHeight,margin:0,lineHeight:i.lineHeights.base,color:i.colors.primary,textDecoration:"none",width:o?"100%":"auto",userSelect:"none","&:visited":{color:i.colors.primary},"&:focus":{outline:"none"},"&:focus-visible":{boxShadow:`0 0 0 0.2rem ${b(i.colors.primary,.5)}`},"&:hover":{textDecoration:"none"},"&:active":{textDecoration:"none"},...x(r,i)})),L=l(e,{target:"e2m15hl1"})(({theme:o})=>({backgroundColor:o.colors.primary,color:o.colors.white,border:`${o.sizes.borderWidth} solid ${o.colors.primary}`,"&:hover":{backgroundColor:v(o.colors.primary,.05),color:o.colors.white},"&:active":{backgroundColor:"transparent",color:o.colors.primary},"&:visited:not(:active)":{color:o.colors.white},"&[disabled], &[disabled]:hover, &[disabled]:active, &[disabled]:visited":{borderColor:o.colors.borderColor,backgroundColor:o.colors.transparent,color:o.colors.fadedText40,cursor:"not-allowed"}})),$=l(e,{target:"e2m15hl2"})(({theme:o})=>({backgroundColor:o.colors.lightenedBg05,color:o.colors.bodyText,border:`${o.sizes.borderWidth} solid ${o.colors.borderColor}`,"&:visited":{color:o.colors.bodyText},"&:hover":{borderColor:o.colors.primary,color:o.colors.primary},"&:active":{color:o.colors.white,borderColor:o.colors.primary,backgroundColor:o.colors.primary},"&:focus:not(:active)":{borderColor:o.colors.primary,color:o.colors.primary},"&[disabled], &[disabled]:hover, &[disabled]:active":{borderColor:o.colors.borderColor,backgroundColor:o.colors.transparent,color:o.colors.fadedText40,cursor:"not-allowed"}})),w=l(e,{target:"e2m15hl3"})(({theme:o})=>({padding:o.spacing.none,backgroundColor:o.colors.transparent,color:o.colors.bodyText,border:"none","&:visited":{color:o.colors.bodyText},"&:hover":{color:o.colors.primary},"&:active":{color:o.colors.primary},"&:focus":{outline:"none"},"&:focus-visible":{color:o.colors.primary,boxShadow:`0 0 0 0.2rem ${b(o.colors.primary,.5)}`},"&[disabled], &[disabled]:hover, &[disabled]:active":{backgroundColor:o.colors.transparent,color:o.colors.fadedText40,cursor:"not-allowed"}}));function T({kind:o,size:r,disabled:i,containerWidth:t,children:c,autoFocus:p,href:g,rel:y,target:f,onClick:C}){let d=L;return o===s.SECONDARY?d=$:o===s.TERTIARY&&(d=w),a(d,{kind:o,size:r||n.MEDIUM,containerWidth:t||!1,disabled:i||!1,autoFocus:p||!1,href:g,target:f,rel:y,onClick:C,tabIndex:i?-1:0,"data-testid":`stBaseLinkButton-${o}`,children:c})}const z=u.memo(T);function R(o){const{element:r}=o;let i=s.SECONDARY;r.type==="primary"?i=s.PRIMARY:r.type==="tertiary"&&(i=s.TERTIARY);const t=c=>{r.disabled&&c.preventDefault()};return a(S,{className:"stLinkButton","data-testid":"stLinkButton",children:a(B,{help:r.help,containerWidth:r.useContainerWidth,children:a(z,{kind:i,size:n.SMALL,disabled:r.disabled,onClick:t,containerWidth:r.useContainerWidth,href:r.url,target:"_blank",rel:"noreferrer","aria-disabled":r.disabled,children:a(k,{icon:r.icon,label:r.label})})})})}const W=u.memo(R);export{W as default};
