# Source: https://github.com/stopwords-iso/stopwords-lv

STOP_WORDS = set(
    """
aiz
ap
apakš
apakšpus
ar
arī
augšpus
bet
bez
bija
biji
biju
bijām
bijāt
būs
būsi
būsiet
būsim
būt
būšu
caur
diemžēl
diezin
droši
dēļ
esam
esat
esi
esmu
gan
gar
iekam
iekams
iekām
iekāms
iekš
iekšpus
ik
ir
it
itin
iz
ja
jau
jeb
jebšu
jel
jo
jā
ka
kamēr
kaut
kolīdz
kopš
kā
kļuva
kļuvi
kļuvu
kļuvām
kļuvāt
kļūs
kļūsi
kļūsiet
kļūsim
kļūst
kļūstam
kļūstat
kļūsti
kļūstu
kļūt
kļūšu
labad
lai
lejpus
līdz
līdzko
ne
nebūt
nedz
nekā
nevis
nezin
no
nu
nē
otrpus
pa
par
pat
pie
pirms
pret
priekš
pār
pēc
starp
tad
tak
tapi
taps
tapsi
tapsiet
tapsim
tapt
tapāt
tapšu
taču
te
tiec
tiek
tiekam
tiekat
tieku
tik
tika
tikai
tiki
tikko
tiklab
tiklīdz
tiks
tiksiet
tiksim
tikt
tiku
tikvien
tikām
tikāt
tikšu
tomēr
topat
turpretim
turpretī
tā
tādēļ
tālab
tāpēc
un
uz
vai
var
varat
varēja
varēji
varēju
varējām
varējāt
varēs
varēsi
varēsiet
varēsim
varēt
varēšu
vien
virs
virspus
vis
viņpus
zem
ārpus
šaipus
""".split()
)
