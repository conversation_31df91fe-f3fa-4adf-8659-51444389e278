import{n as U,r as a,bj as R,j as m,l as w}from"./index.C1z8KpLA.js";const A=U("iframe",{target:"eymnmwl0"})(({theme:r})=>({colorScheme:"normal",border:"none",padding:r.spacing.none,margin:r.spacing.none,width:"100%",aspectRatio:"16 / 9"})),O=w.getLogger("Video"),P={width:"100%"};function $({element:r,endpoints:i,elementMgr:p}){const s=a.useRef(null),{type:v,url:f,startTime:n,subtitles:u,endTime:c,loop:l,autoplay:S,muted:g}=r,T=a.useMemo(()=>{if(!r.id)return!0;const e=p.getElementState(r.id,"preventAutoplay");return e||p.setElementState(r.id,"preventAutoplay",!0),e??!1},[r.id,p]),E=a.useMemo(()=>JSON.stringify(u?u.map(e=>i.buildMediaURL(`${e.url}`)):[]),[u,i]);a.useEffect(()=>{const e=JSON.parse(E);e.length!==0&&e.forEach(t=>{i.checkSourceUrlResponse(t,"Video Subtitle")})},[E,i]),a.useEffect(()=>{s.current&&(s.current.currentTime=n)},[n]),a.useEffect(()=>{const e=s.current,t=()=>{e&&(e.currentTime=r.startTime)};return e&&e.addEventListener("loadedmetadata",t),()=>{e&&e.removeEventListener("loadedmetadata",t)}},[r]),a.useEffect(()=>{const e=s.current;if(!e)return;let t=!1;const d=()=>{c>0&&e.currentTime>=c&&(l?(e.currentTime=n||0,e.play()):t||(t=!0,e.pause()))};return c>0&&e.addEventListener("timeupdate",d),()=>{e&&c>0&&e.removeEventListener("timeupdate",d)}},[c,l,n]),a.useEffect(()=>{const e=s.current;if(!e)return;const t=()=>{l&&(e.currentTime=n||0,e.play())};return e.addEventListener("ended",t),()=>{e&&e.removeEventListener("ended",t)}},[l,n]);const V=e=>{const{startTime:t,endTime:d,loop:b,autoplay:L,muted:N}=r,o=new URL(e);if(t&&!isNaN(t)&&o.searchParams.append("start",t.toString()),d&&!isNaN(d)&&o.searchParams.append("end",d.toString()),b){o.searchParams.append("loop","1");const y=o.pathname.split("/").pop();y&&o.searchParams.append("playlist",y)}return L&&o.searchParams.append("autoplay","1"),N&&o.searchParams.append("mute","1"),o.toString()};if(v===R.Type.YOUTUBE_IFRAME)return m(A,{className:"stVideo","data-testid":"stVideo",title:f,src:V(f),allow:"autoplay; encrypted-media",allowFullScreen:!0});const h=e=>{const t=e.currentTarget.src;O.error(`Client Error: Video source error - ${t}`),i.sendClientErrorToHost("Video","Video source failed to load","onerror triggered",t)};return m("video",{className:"stVideo","data-testid":"stVideo",ref:s,controls:!0,muted:g,autoPlay:S&&!T,src:i.buildMediaURL(f),style:P,crossOrigin:void 0,onError:h,children:u&&u.map((e,t)=>m("track",{kind:"captions",src:i.buildMediaURL(`${e.url}`),label:`${e.label}`,default:t===0,"data-testid":"stVideoSubtitle"},t))})}const M=a.memo($);export{M as default};
