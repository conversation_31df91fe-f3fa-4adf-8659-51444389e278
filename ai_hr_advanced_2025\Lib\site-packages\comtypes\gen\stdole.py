from enum import IntFlag

import comtypes.gen._00020430_0000_0000_C000_000000000046_0_2_0 as __wrapper_module__
from comtypes.gen._00020430_0000_0000_C000_000000000046_0_2_0 import (
    <PERSON><PERSON>_ENABLEDEFAULTBOOL, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, typelib_path, OLE_HANDLE,
    FON<PERSON><PERSON>Z<PERSON>, <PERSON>ON<PERSON><PERSON>LD, DISPPARAMS, Gray, <PERSON><PERSON>_XPOS_CONTAINER,
    OLE_CANCELBOOL, OLE_YSIZE_PIXELS, BSTR, OLE_XPOS_PIXELS,
    IEnumVARIANT, StdFont, VARIANT_BOOL, DISPMETHOD,
    OLE_YSIZE_HIMETRIC, Font, <PERSON>LE_YPOS_CONTAINER, IPicture, IFontDisp,
    FONTNAME, Checked, VgaColor, CoClass, FONTSTRIKETHROUGH,
    IPictureDisp, O<PERSON>_YPOS_HIMETRIC, StdPicture, EXCEPINFO, GUID,
    IFont, Library, DISPPROPERTY, ID<PERSON>patch, dispid, HRE<PERSON>LT,
    OLE_XSIZE_PIXELS, <PERSON>LE_XPOS_HIMETRIC, Unchecked,
    OLE_XSIZE_HIMETRIC, Monochrome, IFontEventsDisp,
    OLE_XSIZE_CONTAINER, IUnknown, Color, OLE_OPTEXCLUSIVE, COMMETHOD,
    FONTUNDERSCORE, OLE_YPOS_PIXELS, _lcid, FontEvents, Picture,
    OLE_YSIZE_CONTAINER, Default, _check_version, OLE_COLOR
)


class OLE_TRISTATE(IntFlag):
    Unchecked = 0
    Checked = 1
    Gray = 2


class LoadPictureConstants(IntFlag):
    Default = 0
    Monochrome = 1
    VgaColor = 2
    Color = 4


__all__ = [
    'IPictureDisp', 'LoadPictureConstants', 'OLE_ENABLEDEFAULTBOOL',
    'FONTITALIC', 'OLE_YPOS_HIMETRIC', 'StdPicture', 'typelib_path',
    'OLE_HANDLE', 'IFont', 'Library', 'FONTSIZE', 'FONTBOLD', 'Gray',
    'OLE_XPOS_CONTAINER', 'OLE_XSIZE_PIXELS', 'OLE_XPOS_HIMETRIC',
    'Unchecked', 'OLE_CANCELBOOL', 'OLE_YSIZE_PIXELS',
    'OLE_XSIZE_HIMETRIC', 'OLE_XPOS_PIXELS', 'Monochrome',
    'IFontEventsDisp', 'OLE_XSIZE_CONTAINER', 'StdFont', 'Color',
    'OLE_OPTEXCLUSIVE', 'OLE_YSIZE_HIMETRIC', 'FONTUNDERSCORE',
    'Font', 'OLE_YPOS_CONTAINER', 'IPicture', 'OLE_YPOS_PIXELS',
    'IFontDisp', 'FontEvents', 'Picture', 'OLE_YSIZE_CONTAINER',
    'Default', 'FONTNAME', 'Checked', 'OLE_TRISTATE', 'VgaColor',
    'OLE_COLOR', 'FONTSTRIKETHROUGH'
]

