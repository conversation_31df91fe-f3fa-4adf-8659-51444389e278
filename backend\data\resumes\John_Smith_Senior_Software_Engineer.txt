
JOHN SMITH
Senior Software Engineer
Email: <EMAIL> | Phone: (*************
LinkedIn: linkedin.com/in/johnsmith | GitHub: github.com/johnsmith

PROFESSIONAL SUMMARY
Experienced Senior Software Engineer with 8+ years of expertise in full-stack development, 
cloud architecture, and team leadership. Proven track record of delivering scalable solutions 
using modern technologies and agile methodologies.

TECHNICAL SKILLS
• Programming Languages: Python, Java, JavaScript, TypeScript, C++
• Frameworks: React, Node.js, Django, Spring Boot, Express.js
• Cloud Platforms: AWS (EC2, S3, Lambda, RDS), Azure, Docker, Kubernetes
• Databases: PostgreSQL, MongoDB, Redis, MySQL
• Tools: Git, Jenkins, JIRA, Confluence, VS Code

PROFESSIONAL EXPERIENCE

Senior Software Engineer | TechCorp Inc. | 2020 - Present
• Led development of microservices architecture serving 1M+ users
• Implemented CI/CD pipelines reducing deployment time by 60%
• Mentored 5 junior developers and conducted code reviews
• Designed and built RESTful APIs with 99.9% uptime
• Collaborated with product managers to define technical requirements

Software Engineer | StartupXYZ | 2018 - 2020
• Developed responsive web applications using React and Node.js
• Optimized database queries improving application performance by 40%
• Integrated third-party APIs and payment gateways
• Participated in agile development cycles and sprint planning

Junior Software Developer | DevSolutions | 2016 - 2018
• Built web applications using Python Django framework
• Wrote unit tests achieving 85% code coverage
• Fixed bugs and implemented new features based on user feedback
• Collaborated with QA team to ensure software quality

EDUCATION
Bachelor of Science in Computer Science
University of Technology | 2012 - 2016
GPA: 3.7/4.0

CERTIFICATIONS
• AWS Certified Solutions Architect - Associate (2021)
• Certified Scrum Master (2020)

PROJECTS
E-commerce Platform (2021)
• Built scalable e-commerce platform using React, Node.js, and AWS
• Implemented secure payment processing and inventory management
• Achieved 99.5% uptime with auto-scaling infrastructure

ACHIEVEMENTS
• Reduced system latency by 50% through performance optimization
• Led successful migration of legacy system to cloud infrastructure
• Received "Employee of the Year" award in 2021
