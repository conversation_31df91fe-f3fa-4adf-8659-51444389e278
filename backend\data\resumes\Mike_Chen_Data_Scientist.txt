
MIKE CHEN
Data Scientist
Email: <EMAIL> | Phone: (*************
LinkedIn: linkedin.com/in/mikechen | GitHub: github.com/mikechen

PROFESSIONAL SUMMARY
Results-driven Data Scientist with 4+ years of experience in machine learning, 
statistical analysis, and data visualization. Proven ability to extract actionable 
insights from complex datasets and drive business decisions.

TECHNICAL SKILLS
• Programming Languages: Python, R, SQL, Scala, Java
• Machine Learning: Scikit-learn, TensorFlow, PyTorch, Keras, XGBoost
• Data Visualization: Tableau, Power BI, Matplotlib, Seaborn, Plotly
• Big Data: Spark, Hadoop, Kafka, Airflow
• Cloud Platforms: AWS (SageMaker, EMR), GCP (BigQuery, Vertex AI)
• Databases: PostgreSQL, MongoDB, Cassandra, Snowflake

PROFESSIONAL EXPERIENCE

Data Scientist | DataTech Analytics | 2021 - Present
• Developed machine learning models improving customer retention by 25%
• Built recommendation system increasing revenue by $2M annually
• Implemented A/B testing framework for product optimization
• Created automated reporting dashboards using Tableau and Python
• Collaborated with cross-functional teams to define KPIs and metrics

Junior Data Scientist | InsightCorp | 2020 - 2021
• Analyzed customer behavior data to identify growth opportunities
• Built predictive models for demand forecasting with 90% accuracy
• Performed statistical analysis and hypothesis testing
• Created data pipelines for real-time analytics
• Presented findings to stakeholders and executive leadership

Data Analyst | StartupAnalytics | 2019 - 2020
• Cleaned and processed large datasets using Python and SQL
• Created interactive dashboards for business intelligence
• Conducted exploratory data analysis to identify trends
• Supported marketing campaigns with data-driven insights
• Automated reporting processes reducing manual work by 70%

EDUCATION
Master of Science in Data Science
Data Science University | 2017 - 2019
Thesis: "Deep Learning for Time Series Forecasting"

Bachelor of Science in Statistics
Statistics College | 2013 - 2017
GPA: 3.8/4.0

CERTIFICATIONS
• AWS Certified Machine Learning - Specialty (2022)
• Google Cloud Professional Data Engineer (2021)
• Tableau Desktop Specialist (2020)

PROJECTS
Customer Churn Prediction (2022)
• Developed ensemble model predicting customer churn with 92% accuracy
• Implemented feature engineering and hyperparameter tuning
• Deployed model to production using AWS SageMaker

Stock Price Prediction (2021)
• Built LSTM neural network for stock price forecasting
• Achieved 15% improvement over baseline models
• Created real-time prediction dashboard

ACHIEVEMENTS
• Reduced customer acquisition cost by 30% through predictive modeling
• Published research paper on "Machine Learning in Finance" (2021)
• Won company hackathon for innovative data solution (2022)
