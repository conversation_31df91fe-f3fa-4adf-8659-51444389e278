"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
*!
Copyright (c) Streamlit Inc. (2018-2022) Snowflake Inc. (2022-2025)

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
"""

import builtins
import collections.abc
import google.protobuf.descriptor
import google.protobuf.internal.containers
import google.protobuf.message
import streamlit.proto.ArrowNamedDataSet_pb2
import streamlit.proto.Arrow_pb2
import typing

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

@typing.final
class ArrowVegaLiteChart(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    SPEC_FIELD_NUMBER: builtins.int
    DATA_FIELD_NUMBER: builtins.int
    DATASETS_FIELD_NUMBER: builtins.int
    USE_CONTAINER_WIDTH_FIELD_NUMBER: builtins.int
    THEME_FIELD_NUMBER: builtins.int
    ID_FIELD_NUMBER: builtins.int
    SELECTION_MODE_FIELD_NUMBER: builtins.int
    FORM_ID_FIELD_NUMBER: builtins.int
    spec: builtins.str
    """The a JSON-formatted string with the Vega-Lite spec."""
    use_container_width: builtins.bool
    """If True, will overwrite the chart width spec to fit to container."""
    theme: builtins.str
    """override the properties with a theme. Currently, only "streamlit" or None are accepted."""
    id: builtins.str
    """ID, required for selection events."""
    form_id: builtins.str
    """The form ID of the widget, this is required if selections are activated on the chart."""
    @property
    def data(self) -> streamlit.proto.Arrow_pb2.Arrow:
        """The dataframe that will be used as the chart's main data source, if
        specified using Vega-Lite's inline API.
        """

    @property
    def datasets(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[streamlit.proto.ArrowNamedDataSet_pb2.ArrowNamedDataSet]:
        """Dataframes associated with this chart using Vega-Lite's datasets API, if
        any. The data is either in `data` field or in the `datasets` field.
        """

    @property
    def selection_mode(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]:
        """Named selection parameters that are activated to trigger reruns."""

    def __init__(
        self,
        *,
        spec: builtins.str = ...,
        data: streamlit.proto.Arrow_pb2.Arrow | None = ...,
        datasets: collections.abc.Iterable[streamlit.proto.ArrowNamedDataSet_pb2.ArrowNamedDataSet] | None = ...,
        use_container_width: builtins.bool = ...,
        theme: builtins.str = ...,
        id: builtins.str = ...,
        selection_mode: collections.abc.Iterable[builtins.str] | None = ...,
        form_id: builtins.str = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["data", b"data"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["data", b"data", "datasets", b"datasets", "form_id", b"form_id", "id", b"id", "selection_mode", b"selection_mode", "spec", b"spec", "theme", b"theme", "use_container_width", b"use_container_width"]) -> None: ...

global___ArrowVegaLiteChart = ArrowVegaLiteChart
